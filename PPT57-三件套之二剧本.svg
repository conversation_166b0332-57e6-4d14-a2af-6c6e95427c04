<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#F5A623">
    三件套之二：剧本 (Playbook)
  </text>
  
  <!-- GPS导航图标 -->
  <g transform="translate(400, 400)">
    <!-- GPS设备 -->
    <rect x="-60" y="-80" width="120" height="160" fill="#333333" rx="15" />
    <rect x="-50" y="-70" width="100" height="120" fill="#87CEEB" rx="10" />
    
    <!-- 地图路线 -->
    <path d="M -30 -50 Q 0 -30 30 -10 Q 20 20 -10 40 Q -30 50 -40 30" 
          stroke="#F5A623" stroke-width="4" fill="none" />
    
    <!-- 路径点 -->
    <circle cx="-30" cy="-50" r="5" fill="#005A9E" />
    <circle cx="30" cy="-10" r="5" fill="#005A9E" />
    <circle cx="-10" cy="40" r="5" fill="#005A9E" />
    
    <!-- 当前位置 -->
    <circle cx="-30" cy="-50" r="8" fill="#FF0000" opacity="0.7" />
    <circle cx="-30" cy="-50" r="12" fill="#FF0000" opacity="0.3" />
    
    <!-- 分叉路口标记 -->
    <path d="M 30 -10 L 50 -20" stroke="#F5A623" stroke-width="3" />
    <path d="M 30 -10 L 50 0" stroke="#F5A623" stroke-width="3" />
    <text x="55" y="-15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#005A9E">A</text>
    <text x="55" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#005A9E">B</text>
    
    <!-- GPS信号 -->
    <g transform="translate(0, -100)" opacity="0.6">
      <circle cx="0" cy="0" r="15" fill="none" stroke="#00FF00" stroke-width="2" />
      <circle cx="0" cy="0" r="25" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.7" />
      <circle cx="0" cy="0" r="35" fill="none" stroke="#00FF00" stroke-width="2" opacity="0.4" />
    </g>
  </g>
  
  <!-- 核心作用 -->
  <rect x="700" y="250" width="1000" height="100" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="1200" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#F5A623">
    核心作用：导航决策的"GPS"
  </text>
  
  <!-- 适用场景 -->
  <rect x="700" y="400" width="1000" height="350" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1200" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    适用场景：
  </text>
  
  <text x="750" y="510" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 非线性的、多分支的交互场景
  </text>
  
  <text x="750" y="570" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 需要在"岔路口"做决策的情况
  </text>
  
  <text x="750" y="630" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#666666">
    (如：客户异议处理、故障诊断流程)
  </text>
  
  <!-- 装饰决策树 -->
  <g transform="translate(200, 650)">
    <!-- 决策节点 -->
    <circle cx="75" cy="0" r="20" fill="#F5A623" />
    <text x="75" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#FFFFFF">?</text>
    
    <!-- 分支 -->
    <line x1="75" y1="20" x2="30" y2="60" stroke="#005A9E" stroke-width="3" />
    <line x1="75" y1="20" x2="120" y2="60" stroke="#005A9E" stroke-width="3" />
    
    <!-- 选项A -->
    <rect x="10" y="50" width="40" height="20" fill="#005A9E" rx="5" />
    <text x="30" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">选项A</text>
    
    <!-- 选项B -->
    <rect x="100" y="50" width="40" height="20" fill="#005A9E" rx="5" />
    <text x="120" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">选项B</text>
    
    <!-- 结果 -->
    <line x1="30" y1="70" x2="30" y2="100" stroke="#F5A623" stroke-width="2" />
    <line x1="120" y1="70" x2="120" y2="100" stroke="#F5A623" stroke-width="2" />
    
    <circle cx="30" cy="110" r="8" fill="#00FF00" />
    <circle cx="120" cy="110" r="8" fill="#00FF00" />
  </g>
</svg>
