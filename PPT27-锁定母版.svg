<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    从案例到真人，锁定你的"母版"
  </text>
  
  <!-- 背景转换效果 -->
  <defs>
    <linearGradient id="transitionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#CCCCCC;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#F5A623;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#005A9E;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  <rect x="200" y="250" width="1520" height="400" fill="url(#transitionGradient)" rx="20" />
  
  <!-- 文档图标 (左侧) -->
  <rect x="300" y="350" width="200" height="250" fill="#FFFFFF" stroke="#005A9E" stroke-width="3" rx="10" />
  <line x1="320" y1="380" x2="480" y2="380" stroke="#005A9E" stroke-width="2" />
  <line x1="320" y1="410" x2="460" y2="410" stroke="#005A9E" stroke-width="2" />
  <line x1="320" y1="440" x2="470" y2="440" stroke="#005A9E" stroke-width="2" />
  <line x1="320" y1="470" x2="450" y2="470" stroke="#005A9E" stroke-width="2" />
  <text x="400" y="530" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#005A9E">
    案例文档
  </text>
  
  <!-- 转换箭头 -->
  <path d="M 550 475 L 750 475" stroke="#F5A623" stroke-width="15" marker-end="url(#transformArrow)" />
  <text x="650" y="460" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
    转向
  </text>
  
  <!-- 望远镜瞄准人剪影 (右侧) -->
  <circle cx="1000" cy="400" r="80" fill="#005A9E" opacity="0.3" />
  <ellipse cx="1000" cy="450" rx="40" ry="60" fill="#333333" opacity="0.7" />
  <circle cx="1000" cy="420" r="20" fill="#333333" opacity="0.7" />
  
  <!-- 望远镜 -->
  <rect x="1150" y="380" width="100" height="40" fill="#F5A623" rx="20" />
  <circle cx="1130" cy="400" r="25" fill="#F5A623" opacity="0.7" />
  <circle cx="1270" cy="400" r="15" fill="#005A9E" />
  <line x1="1100" y1="400" x2="1130" y2="400" stroke="#005A9E" stroke-width="4" />
  
  <!-- 瞄准线 -->
  <line x1="1080" y1="400" x2="1040" y2="400" stroke="#F5A623" stroke-width="3" stroke-dasharray="10,5" />
  <circle cx="1000" cy="400" r="5" fill="#F5A623" />
  
  <!-- 下一项任务 -->
  <rect x="300" y="720" width="1320" height="200" fill="#FFFFFF" opacity="0.9" stroke="#005A9E" stroke-width="3" rx="20" />
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    下一项任务：
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#333333">
    完成你的第一份《战法母版提名与背景分析表》
  </text>
  
  <!-- 底部说明 -->
  <text x="960" y="980" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-style="italic" fill="#F5A623">
    这是一次独立的、深度的思考。这是你训后行动的正式起点。
  </text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="transformArrow" markerWidth="20" markerHeight="14" refX="18" refY="7" orient="auto">
      <polygon points="0 0, 20 7, 0 14" fill="#F5A623" />
    </marker>
  </defs>
</svg>
