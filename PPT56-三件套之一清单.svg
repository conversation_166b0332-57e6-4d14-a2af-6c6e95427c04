<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    三件套之一：清单 (Checklist)
  </text>
  
  <!-- 巨大护盾图标 -->
  <g transform="translate(400, 400)">
    <!-- 护盾外形 -->
    <path d="M 0 -100 Q -60 -80 -80 -20 Q -80 40 -60 80 Q 0 120 0 120 Q 0 120 60 80 Q 80 40 80 -20 Q 60 -80 0 -100 Z" 
          fill="#005A9E" opacity="0.8" />
    <path d="M 0 -80 Q -45 -65 -60 -15 Q -60 30 -45 60 Q 0 90 0 90 Q 0 90 45 60 Q 60 30 60 -15 Q 45 -65 0 -80 Z" 
          fill="#F5A623" opacity="0.6" />
    
    <!-- 护盾中心的勾选标记 -->
    <path d="M -20 0 L -5 15 L 25 -15" stroke="#FFFFFF" stroke-width="8" fill="none" stroke-linecap="round" />
    
    <!-- 护盾光芒 -->
    <g opacity="0.4">
      <line x1="0" y1="-120" x2="0" y2="-140" stroke="#FFD700" stroke-width="4" />
      <line x1="85" y1="-25" x2="100" y2="-35" stroke="#FFD700" stroke-width="3" />
      <line x1="85" y1="25" x2="100" y2="35" stroke="#FFD700" stroke-width="3" />
      <line x1="-85" y1="-25" x2="-100" y2="-35" stroke="#FFD700" stroke-width="3" />
      <line x1="-85" y1="25" x2="-100" y2="35" stroke="#FFD700" stroke-width="3" />
    </g>
  </g>
  
  <!-- 核心作用 -->
  <rect x="700" y="250" width="1000" height="100" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1200" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    核心作用：防御遗忘的"护盾"
  </text>
  
  <!-- 适用场景 -->
  <rect x="700" y="400" width="1000" height="350" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="1200" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    适用场景：
  </text>
  
  <text x="750" y="510" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 线性的、可重复的流程
  </text>
  
  <text x="750" y="570" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 关键步骤一步都不能错的场景
  </text>
  
  <text x="750" y="630" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#666666">
    (如：客户拜访前准备、设备安装标准流程)
  </text>
  
  <!-- 装饰清单图标 -->
  <g transform="translate(200, 650)">
    <rect x="0" y="0" width="150" height="200" fill="#FFFFFF" stroke="#005A9E" stroke-width="4" rx="10" />
    
    <!-- 清单项目 -->
    <rect x="20" y="30" width="15" height="15" fill="none" stroke="#005A9E" stroke-width="2" />
    <path d="M 22 37 L 27 42 L 33 32" stroke="#F5A623" stroke-width="3" fill="none" />
    <line x1="50" y1="37" x2="120" y2="37" stroke="#333333" stroke-width="2" />
    
    <rect x="20" y="60" width="15" height="15" fill="none" stroke="#005A9E" stroke-width="2" />
    <path d="M 22 67 L 27 72 L 33 62" stroke="#F5A623" stroke-width="3" fill="none" />
    <line x1="50" y1="67" x2="120" y2="67" stroke="#333333" stroke-width="2" />
    
    <rect x="20" y="90" width="15" height="15" fill="none" stroke="#005A9E" stroke-width="2" />
    <line x1="50" y1="97" x2="120" y2="97" stroke="#333333" stroke-width="2" />
    
    <rect x="20" y="120" width="15" height="15" fill="none" stroke="#005A9E" stroke-width="2" />
    <line x1="50" y1="127" x2="120" y2="127" stroke="#333333" stroke-width="2" />
  </g>
</svg>
