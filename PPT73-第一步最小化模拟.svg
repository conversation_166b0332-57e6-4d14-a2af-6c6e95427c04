<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#4CAF50">
    第一步：最小化模拟 (Minimized Simulation)
  </text>
  
  <!-- 核心理念 -->
  <rect x="200" y="200" width="1520" height="120" fill="#4CAF50" opacity="0.1" rx="20" />
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#4CAF50">
    核心理念：
  </text>
  <text x="960" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    降低心理门槛，消除行动恐惧，让学员敢于"开口"
  </text>
  
  <!-- 三个具体方法 -->
  <!-- 方法1 -->
  <rect x="150" y="370" width="500" height="280" fill="#FFFFFF" stroke="#4CAF50" stroke-width="4" rx="20" />
  <rect x="150" y="370" width="500" height="60" fill="#4CAF50" opacity="0.2" rx="20" />
  
  <text x="400" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#4CAF50">
    方法一：角色扮演
  </text>
  
  <!-- 面具图标 -->
  <g transform="translate(200, 480)">
    <ellipse cx="0" cy="0" rx="30" ry="25" fill="#FFD700" />
    <ellipse cx="-10" cy="-8" rx="5" ry="8" fill="#333333" />
    <ellipse cx="10" cy="-8" rx="5" ry="8" fill="#333333" />
    <path d="M -8 8 Q 0 15 8 8" stroke="#333333" stroke-width="3" fill="none" />
  </g>
  
  <text x="400" y="480" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    "你现在是资深销售张总，
  </text>
  <text x="400" y="510" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    我是新客户李总，
  </text>
  <text x="400" y="540" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    请用你的清单，
  </text>
  <text x="400" y="570" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    向我介绍一下产品。"
  </text>
  
  <!-- 方法2 -->
  <rect x="710" y="370" width="500" height="280" fill="#FFFFFF" stroke="#4CAF50" stroke-width="4" rx="20" />
  <rect x="710" y="370" width="500" height="60" fill="#4CAF50" opacity="0.2" rx="20" />
  
  <text x="960" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#4CAF50">
    方法二：片段练习
  </text>
  
  <!-- 拼图图标 -->
  <g transform="translate(760, 480)">
    <rect x="0" y="0" width="25" height="25" fill="#005A9E" />
    <circle cx="25" cy="12.5" r="5" fill="#005A9E" />
    <rect x="35" y="0" width="25" height="25" fill="#F5A623" />
    <circle cx="30" cy="12.5" r="5" fill="#FFFFFF" />
  </g>
  
  <text x="960" y="480" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    "我们先只练习开场白，
  </text>
  <text x="960" y="510" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    不用考虑后面的环节。
  </text>
  <text x="960" y="540" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    就练这30秒，
  </text>
  <text x="960" y="570" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    说到自然为止。"
  </text>
  
  <!-- 方法3 -->
  <rect x="1270" y="370" width="500" height="280" fill="#FFFFFF" stroke="#4CAF50" stroke-width="4" rx="20" />
  <rect x="1270" y="370" width="500" height="60" fill="#4CAF50" opacity="0.2" rx="20" />
  
  <text x="1520" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#4CAF50">
    方法三：同伴练习
  </text>
  
  <!-- 握手图标 -->
  <g transform="translate(1320, 480)">
    <rect x="0" y="0" width="15" height="25" fill="#005A9E" rx="7" />
    <rect x="20" y="0" width="15" height="25" fill="#F5A623" rx="7" />
    <rect x="10" y="10" width="15" height="10" fill="#4CAF50" rx="5" />
  </g>
  
  <text x="1520" y="480" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    "两人一组，
  </text>
  <text x="1520" y="510" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    互相当对方的客户，
  </text>
  <text x="1520" y="540" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    轮流练习，
  </text>
  <text x="1520" y="570" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    互相给反馈。"
  </text>
  
  <!-- 底部关键提醒 -->
  <rect x="200" y="700" width="1520" height="150" fill="#FFD700" opacity="0.1" rx="20" />
  <text x="960" y="760" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    关键提醒：
  </text>
  <text x="960" y="810" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    这一步的目标不是"做对"，而是"敢做"！
  </text>
  
  <!-- 装饰温度计 -->
  <g transform="translate(150, 750)">
    <rect x="0" y="-30" width="8" height="60" fill="#4CAF50" rx="4" />
    <circle cx="4" cy="35" r="12" fill="#4CAF50" />
    <text x="25" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#4CAF50">
      低压环境
    </text>
  </g>
</svg>
