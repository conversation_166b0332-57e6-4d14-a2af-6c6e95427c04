<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 温暖背景 -->
  <defs>
    <radialGradient id="warmGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#FFF8DC;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#F0E68C;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#DDD;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#warmGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#8B4513" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#8B4513" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#8B4513" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#8B4513" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#8B4513">
    讲师寄语
  </text>
  
  <!-- 讲师头像装饰 -->
  <g transform="translate(300, 300)">
    <circle cx="0" cy="0" r="80" fill="#005A9E" />
    <circle cx="0" cy="-20" r="25" fill="#F5DEB3" />
    <rect x="-30" y="10" width="60" height="70" fill="#005A9E" />
    <rect x="-25" y="15" width="50" height="15" fill="#FFFFFF" />
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#005A9E">
      首席讲师
    </text>
  </g>
  
  <!-- 寄语内容 -->
  <rect x="500" y="250" width="1200" height="600" fill="#FFFFFF" opacity="0.9" stroke="#8B4513" stroke-width="4" rx="20" />
  
  <!-- 引号装饰 -->
  <text x="550" y="320" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" fill="#F5A623" opacity="0.3">
    "
  </text>
  
  <!-- 寄语正文 -->
  <g transform="translate(600, 350)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#005A9E">
      亲爱的战法工程师们：
    </text>
    
    <text x="0" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      三天的学习即将结束，但你们的工程师之路才刚刚开始。
    </text>
    
    <text x="0" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      记住，真正的工程师不是因为掌握了多少工具而伟大，
    </text>
    <text x="0" y="150" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      而是因为能够将复杂的经验转化为简单的工具，
    </text>
    <text x="0" y="190" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      让更多的人能够站在巨人的肩膀上前行。
    </text>
    
    <text x="0" y="250" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      愿你们在各自的战场上，用深度思考去交换一线的"免思考"，
    </text>
    <text x="0" y="290" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      用个人的洞察去点亮团队的智慧，
    </text>
    <text x="0" y="330" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="26" fill="#333333">
      用今天的学习去创造明天的传奇。
    </text>
    
    <text x="0" y="390" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
      记住：你们不再是知识的搬运工，
    </text>
    <text x="0" y="430" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
      而是智慧的工程师！
    </text>
  </g>
  
  <!-- 结尾引号 -->
  <text x="1600" y="780" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" fill="#F5A623" opacity="0.3">
    "
  </text>
  
  <!-- 签名 -->
  <g transform="translate(1400, 800)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#8B4513">
      ——— 讲师团队
    </text>
    <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
      2024年 · 战法工程师训练营
    </text>
  </g>
  
  <!-- 装饰元素 -->
  <!-- 左侧装饰花纹 -->
  <g transform="translate(200, 600)" opacity="0.4">
    <path d="M 0 0 Q 20 -20 40 0 Q 60 20 80 0" stroke="#8B4513" stroke-width="3" fill="none" />
    <circle cx="20" cy="-10" r="3" fill="#F5A623" />
    <circle cx="60" cy="10" r="3" fill="#F5A623" />
  </g>
  
  <!-- 右侧装饰花纹 -->
  <g transform="translate(1640, 600)" opacity="0.4">
    <path d="M 0 0 Q -20 -20 -40 0 Q -60 20 -80 0" stroke="#8B4513" stroke-width="3" fill="none" />
    <circle cx="-20" cy="-10" r="3" fill="#F5A623" />
    <circle cx="-60" cy="10" r="3" fill="#F5A623" />
  </g>
  
  <!-- 装饰心形 -->
  <g transform="translate(450, 400)" opacity="0.3">
    <path d="M 0 10 C 0 5, 5 0, 10 0 C 15 0, 20 5, 20 10 C 20 15, 10 25, 0 35 C -10 25, -20 15, -20 10 C -20 5, -15 0, -10 0 C -5 0, 0 5, 0 10 Z" fill="#FF69B4" />
  </g>
  
  <g transform="translate(1500, 500)" opacity="0.3">
    <path d="M 0 8 C 0 4, 4 0, 8 0 C 12 0, 16 4, 16 8 C 16 12, 8 20, 0 28 C -8 20, -16 12, -16 8 C -16 4, -12 0, -8 0 C -4 0, 0 4, 0 8 Z" fill="#FF69B4" />
  </g>
  
  <!-- 装饰星星 -->
  <g transform="translate(400, 250)" opacity="0.5">
    <polygon points="0,-12 3,0 12,0 5,6 8,18 0,12 -8,18 -5,6 -12,0 -3,0" fill="#FFD700" />
  </g>
  
  <g transform="translate(1550, 350)" opacity="0.5">
    <polygon points="0,-10 2.5,0 10,0 4,5 6,15 0,10 -6,15 -4,5 -10,0 -2.5,0" fill="#FFD700" />
  </g>
  
  <!-- 底部装饰线 -->
  <line x1="300" y1="900" x2="1620" y2="900" stroke="#8B4513" stroke-width="4" />
  <line x1="400" y1="920" x2="1520" y2="920" stroke="#F5A623" stroke-width="2" />
  
  <!-- 装饰羽毛 -->
  <g transform="translate(250, 750)">
    <!-- 羽毛主体 -->
    <path d="M 0 0 Q -8 -20 0 -40 Q 8 -45 15 -40 Q 20 -20 10 0" fill="#8B4513" opacity="0.6" />
    <!-- 羽毛纹理 -->
    <path d="M 2 -5 Q -5 -15 0 -25" stroke="#654321" stroke-width="1" fill="none" />
    <path d="M 8 -8 Q 15 -18 12 -28" stroke="#654321" stroke-width="1" fill="none" />
    <path d="M 5 -12 Q 12 -22 8 -32" stroke="#654321" stroke-width="1" fill="none" />
  </g>
</svg>
