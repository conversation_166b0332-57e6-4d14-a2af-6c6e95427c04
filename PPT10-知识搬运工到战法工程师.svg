<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    从"知识搬运工"到"战法工程师"
  </text>
  
  <!-- 中央分割线 -->
  <line x1="960" y1="200" x2="960" y2="900" stroke="#F5A623" stroke-width="8" />
  
  <!-- 左侧 - 知识搬运工 (灰色调) -->
  <rect x="150" y="220" width="760" height="650" fill="#333333" opacity="0.1" rx="20" />
  
  <!-- 搬运工图标 -->
  <rect x="400" y="260" width="60" height="40" fill="#666666" rx="5" />
  <circle cx="430" cy="320" r="15" fill="#666666" />
  <rect x="420" y="335" width="20" height="40" fill="#666666" />
  <rect x="410" y="375" width="15" height="25" fill="#666666" />
  <rect x="435" y="375" width="15" height="25" fill="#666666" />
  
  <text x="480" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#666666">
    知识搬运工
  </text>
  
  <text x="200" y="450" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#666666">
    定位: 被动响应者
  </text>
  <text x="200" y="520" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#666666">
    任务: 交付课程
  </text>
  <text x="200" y="590" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#666666">
    产出: PPT/手册
  </text>
  <text x="200" y="660" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#666666">
    衡量: 满意度/时长
  </text>
  
  <!-- 右侧 - 战法工程师 (亮色调) -->
  <rect x="1010" y="220" width="760" height="650" fill="#F5A623" opacity="0.1" rx="20" />
  
  <!-- 工程师图标 -->
  <rect x="1250" y="260" width="80" height="60" fill="#005A9E" rx="5" />
  <circle cx="1290" cy="340" r="20" fill="#005A9E" />
  <rect x="1270" y="360" width="40" height="50" fill="#005A9E" />
  <rect x="1260" y="410" width="20" height="30" fill="#005A9E" />
  <rect x="1300" y="410" width="20" height="30" fill="#005A9E" />
  <rect x="1240" y="270" width="100" height="3" fill="#F5A623" />
  <rect x="1240" y="280" width="80" height="3" fill="#F5A623" />
  
  <text x="1390" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    战法工程师
  </text>
  
  <text x="1060" y="450" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#005A9E">
    定位: 主动设计者
  </text>
  <text x="1060" y="520" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#005A9E">
    任务: 解决问题
  </text>
  <text x="1060" y="590" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#005A9E">
    产出: 系统/工具
  </text>
  <text x="1060" y="660" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#005A9E">
    衡量: 业务结果
  </text>
  
  <!-- 转换箭头 -->
  <path d="M 850 540 L 1070 540" stroke="#F5A623" stroke-width="12" marker-end="url(#bigArrowhead)" />
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="bigArrowhead" markerWidth="20" markerHeight="14" refX="18" refY="7" orient="auto">
      <polygon points="0 0, 20 7, 0 14" fill="#F5A623" />
    </marker>
  </defs>
</svg>
