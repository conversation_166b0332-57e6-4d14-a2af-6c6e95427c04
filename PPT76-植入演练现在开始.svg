<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 训练场背景 -->
  <defs>
    <radialGradient id="trainingGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#005A9E;stop-opacity:0.08" />
      <stop offset="100%" style="stop-color:#F5A623;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#trainingGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    植入演练，现在开始！
  </text>
  
  <!-- 训练营装饰 -->
  <g transform="translate(300, 300)">
    <!-- 训练场地 -->
    <rect x="0" y="50" width="200" height="100" fill="#8FBC8F" opacity="0.6" rx="10" />
    <line x1="0" y1="100" x2="200" y2="100" stroke="#FFFFFF" stroke-width="4" stroke-dasharray="20,10" />
    
    <!-- 训练器材 -->
    <rect x="20" y="20" width="30" height="40" fill="#8B4513" rx="5" />
    <rect x="70" y="10" width="25" height="50" fill="#8B4513" rx="5" />
    <rect x="120" y="25" width="35" height="35" fill="#8B4513" rx="5" />
    
    <!-- 哨子 -->
    <circle cx="250" cy="30" r="15" fill="#FFD700" />
    <rect x="265" y="25" width="20" height="10" fill="#8B4513" rx="5" />
    
    <text x="100" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" fill="#005A9E">
      能力训练场
    </text>
  </g>
  
  <!-- 演练任务 -->
  <rect x="600" y="250" width="1100" height="500" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1150" y="320" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    演练任务：
  </text>
  
  <text x="1150" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    运用"三步植入法"，教会你的同桌使用你的清单
  </text>
  
  <!-- 三个步骤提醒 -->
  <g transform="translate(650, 450)">
    <!-- 步骤1 -->
    <circle cx="0" cy="0" r="25" fill="#4CAF50" />
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
      1
    </text>
    <text x="50" y="8" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      最小化模拟
    </text>
    <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      让对方先"敢做"，降低心理门槛
    </text>
  </g>
  
  <g transform="translate(650, 530)">
    <!-- 步骤2 -->
    <circle cx="0" cy="0" r="25" fill="#005A9E" />
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
      2
    </text>
    <text x="50" y="8" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#005A9E">
      带教式通关
    </text>
    <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      即时反馈，确保动作正确
    </text>
  </g>
  
  <g transform="translate(650, 610)">
    <!-- 步骤3 -->
    <circle cx="0" cy="0" r="25" fill="#F5A623" />
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
      3
    </text>
    <text x="50" y="8" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      实战中认证
    </text>
    <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      模拟真实场景，检验效果
    </text>
  </g>
  
  <!-- 时间和要求 -->
  <rect x="200" y="800" width="1520" height="150" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    时间：30分钟 | 要求：两人一组，轮流当教练
  </text>
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    记住：你们现在是"战法工程师"，不是"知识搬运工"！
  </text>
  
  <!-- 装饰秒表 -->
  <g transform="translate(1500, 400)">
    <circle cx="0" cy="0" r="40" fill="#FFFFFF" stroke="#F5A623" stroke-width="6" />
    <circle cx="0" cy="0" r="30" fill="none" stroke="#005A9E" stroke-width="4" />
    <line x1="0" y1="0" x2="0" y2="-20" stroke="#F5A623" stroke-width="4" />
    <line x1="0" y1="0" x2="15" y2="0" stroke="#F5A623" stroke-width="3" />
    <circle cx="0" cy="0" r="5" fill="#F5A623" />
    <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#F5A623">
      30分钟
    </text>
  </g>
  
  <!-- 装饰哨声线条 -->
  <g transform="translate(200, 400)" opacity="0.4">
    <path d="M 0 0 Q 20 -10 40 0 Q 60 10 80 0" stroke="#FFD700" stroke-width="4" fill="none" />
    <path d="M 0 20 Q 20 10 40 20 Q 60 30 80 20" stroke="#FFD700" stroke-width="3" fill="none" />
    <path d="M 0 40 Q 20 30 40 40 Q 60 50 80 40" stroke="#FFD700" stroke-width="2" fill="none" />
  </g>
</svg>
