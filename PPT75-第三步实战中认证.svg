<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#F5A623">
    第三步：实战中认证 (In-Action Certification)
  </text>
  
  <!-- 核心理念 -->
  <rect x="200" y="200" width="1520" height="120" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    核心理念：
  </text>
  <text x="960" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    连接训练与绩效，打通最后一公里
  </text>
  
  <!-- 实战场景 -->
  <g transform="translate(300, 450)">
    <!-- 战场背景 -->
    <rect x="-50" y="-50" width="300" height="200" fill="#E8F5E8" opacity="0.5" rx="20" />
    
    <!-- 士兵 -->
    <circle cx="0" cy="0" r="30" fill="#4CAF50" />
    <rect x="-20" y="30" width="40" height="50" fill="#4CAF50" />
    <line x1="-30" y1="45" x2="-50" y2="55" stroke="#4CAF50" stroke-width="4" />
    <line x1="30" y1="45" x2="50" y2="55" stroke="#4CAF50" stroke-width="4" />
    
    <!-- 武器 -->
    <rect x="60" y="35" width="40" height="20" fill="#8B4513" rx="5" />
    <text x="80" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">
      清单
    </text>
    
    <!-- 敌人/挑战 -->
    <circle cx="180" cy="0" r="25" fill="#FF6B6B" />
    <rect x="160" y="25" width="40" height="45" fill="#FF6B6B" />
    <text x="180" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#FF6B6B">
      真实挑战
    </text>
    
    <!-- 战斗线 -->
    <path d="M 50 40 L 150 40" stroke="#F5A623" stroke-width="4" stroke-dasharray="10,5" />
    
    <text x="125" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      真实战场
    </text>
  </g>
  
  <!-- 认证标准 -->
  <rect x="600" y="350" width="1100" height="400" fill="#F5A623" opacity="0.05" rx="20" />
  <text x="1150" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    认证标准：
  </text>
  
  <!-- 标准1 -->
  <g transform="translate(650, 450)">
    <rect x="-10" y="-10" width="20" height="20" fill="none" stroke="#4CAF50" stroke-width="3" />
    <path d="M -5 0 L 0 5 L 10 -5" stroke="#4CAF50" stroke-width="3" fill="none" />
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      能够独立完成
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      在真实场景中，无需提醒，能完整执行清单
    </text>
  </g>
  
  <!-- 标准2 -->
  <g transform="translate(650, 520)">
    <rect x="-10" y="-10" width="20" height="20" fill="none" stroke="#4CAF50" stroke-width="3" />
    <path d="M -5 0 L 0 5 L 10 -5" stroke="#4CAF50" stroke-width="3" fill="none" />
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      能够灵活应变
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      遇到意外情况，能基于清单原理做出调整
    </text>
  </g>
  
  <!-- 标准3 -->
  <g transform="translate(650, 590)">
    <rect x="-10" y="-10" width="20" height="20" fill="none" stroke="#4CAF50" stroke-width="3" />
    <path d="M -5 0 L 0 5 L 10 -5" stroke="#4CAF50" stroke-width="3" fill="none" />
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      能够产生结果
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      使用清单后，在关键指标上有明显改善
    </text>
  </g>
  
  <!-- 标准4 -->
  <g transform="translate(650, 660)">
    <rect x="-10" y="-10" width="20" height="20" fill="none" stroke="#4CAF50" stroke-width="3" />
    <path d="M -5 0 L 0 5 L 10 -5" stroke="#4CAF50" stroke-width="3" fill="none" />
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      能够传授他人
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      能向新人解释清单的使用方法和注意事项
    </text>
  </g>
  
  <!-- 底部关键提醒 -->
  <rect x="200" y="800" width="1520" height="120" fill="#FFD700" opacity="0.1" rx="20" />
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    关键提醒：
  </text>
  <text x="960" y="890" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    这一步的目标是"做成"，确保训练真正转化为业务结果！
  </text>
  
  <!-- 装饰认证徽章 -->
  <g transform="translate(1600, 500)">
    <circle cx="0" cy="0" r="40" fill="#FFD700" />
    <circle cx="0" cy="0" r="30" fill="#F5A623" />
    <polygon points="0,-15 10,0 0,15 -10,0" fill="#FFFFFF" />
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#F5A623">
      实战认证
    </text>
  </g>
</svg>
