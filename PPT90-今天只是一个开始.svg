<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 宇宙背景 -->
  <defs>
    <radialGradient id="universeGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#191970;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#483D8B;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#2F4F4F;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#universeGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#FFD700" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#FFD700" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#FFD700" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#FFD700" stroke-width="8" fill="none" />
  
  <!-- 地平线 -->
  <path d="M 0 700 Q 480 650 960 700 Q 1440 750 1920 700" stroke="#4169E1" stroke-width="6" fill="none" />
  
  <!-- 核心金句 -->
  <text x="960" y="350" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#005A9E">
    今天，只是一个开始。
  </text>
  
  <text x="960" y="480" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="72" font-weight="bold" fill="#F5A623">
    真正的传奇，将在你们回到战场后，
  </text>
  
  <text x="960" y="580" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="72" font-weight="bold" fill="#F5A623">
    亲手写就！
  </text>
  
  <!-- 装饰星星 -->
  <g transform="translate(200, 200)" opacity="0.8">
    <polygon points="0,-15 4,0 15,0 6,8 10,23 0,15 -10,23 -6,8 -15,0 -4,0" fill="#FFD700" />
  </g>
  
  <g transform="translate(1720, 250)" opacity="0.8">
    <polygon points="0,-12 3,0 12,0 5,6 8,18 0,12 -8,18 -5,6 -12,0 -3,0" fill="#FFD700" />
  </g>
  
  <g transform="translate(300, 150)" opacity="0.6">
    <polygon points="0,-8 2,0 8,0 3,4 5,12 0,8 -5,12 -3,4 -8,0 -2,0" fill="#FFFFFF" />
  </g>
  
  <g transform="translate(1600, 180)" opacity="0.6">
    <polygon points="0,-10 2.5,0 10,0 4,5 6,15 0,10 -6,15 -4,5 -10,0 -2.5,0" fill="#FFFFFF" />
  </g>
  
  <g transform="translate(150, 300)" opacity="0.4">
    <polygon points="0,-6 1.5,0 6,0 2.5,3 3.5,9 0,6 -3.5,9 -2.5,3 -6,0 -1.5,0" fill="#87CEEB" />
  </g>
  
  <g transform="translate(1770, 320)" opacity="0.4">
    <polygon points="0,-6 1.5,0 6,0 2.5,3 3.5,9 0,6 -3.5,9 -2.5,3 -6,0 -1.5,0" fill="#87CEEB" />
  </g>
  
  <!-- 更多小星星 -->
  <g transform="translate(400, 100)" opacity="0.3">
    <circle cx="0" cy="0" r="2" fill="#FFFFFF" />
  </g>
  <g transform="translate(800, 120)" opacity="0.3">
    <circle cx="0" cy="0" r="1.5" fill="#FFFFFF" />
  </g>
  <g transform="translate(1200, 140)" opacity="0.3">
    <circle cx="0" cy="0" r="2" fill="#FFFFFF" />
  </g>
  <g transform="translate(1500, 160)" opacity="0.3">
    <circle cx="0" cy="0" r="1" fill="#FFFFFF" />
  </g>
  <g transform="translate(600, 80)" opacity="0.3">
    <circle cx="0" cy="0" r="1.5" fill="#FFFFFF" />
  </g>
  <g transform="translate(1000, 90)" opacity="0.3">
    <circle cx="0" cy="0" r="1" fill="#FFFFFF" />
  </g>
  <g transform="translate(1400, 110)" opacity="0.3">
    <circle cx="0" cy="0" r="2" fill="#FFFFFF" />
  </g>
  
  <!-- 装饰流星 -->
  <g transform="translate(500, 250)" opacity="0.7">
    <circle cx="0" cy="0" r="3" fill="#FFD700" />
    <path d="M 0 0 L -20 -8 L -15 -5 L -25 -10" stroke="#FFD700" stroke-width="2" fill="none" />
  </g>
  
  <g transform="translate(1300, 300)" opacity="0.5">
    <circle cx="0" cy="0" r="2" fill="#87CEEB" />
    <path d="M 0 0 L -15 -6 L -12 -4 L -18 -8" stroke="#87CEEB" stroke-width="1.5" fill="none" />
  </g>
  
  <!-- 底部激励文字 -->
  <rect x="200" y="750" width="1520" height="200" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    各位工程师，毕业快乐！
  </text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    期待在战场上，听到你们的捷报！
  </text>
  
  <!-- 装饰光芒 -->
  <g transform="translate(960, 400)" opacity="0.3">
    <line x1="0" y1="0" x2="0" y2="-100" stroke="#FFD700" stroke-width="3" />
    <line x1="0" y1="0" x2="71" y2="-71" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="100" y2="0" stroke="#FFD700" stroke-width="3" />
    <line x1="0" y1="0" x2="71" y2="71" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="0" y2="100" stroke="#FFD700" stroke-width="3" />
    <line x1="0" y1="0" x2="-71" y2="71" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="-100" y2="0" stroke="#FFD700" stroke-width="3" />
    <line x1="0" y1="0" x2="-71" y2="-71" stroke="#FFD700" stroke-width="2" />
    
    <!-- 次级光芒 -->
    <line x1="0" y1="0" x2="35" y2="-93" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="93" y2="-35" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="93" y2="35" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="35" y2="93" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="-35" y2="93" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="-93" y2="35" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="-93" y2="-35" stroke="#FFD700" stroke-width="1" />
    <line x1="0" y1="0" x2="-35" y2="-93" stroke="#FFD700" stroke-width="1" />
  </g>
  
  <!-- 装饰云朵 -->
  <g transform="translate(200, 650)" opacity="0.4">
    <ellipse cx="0" cy="0" rx="30" ry="15" fill="#FFFFFF" />
    <ellipse cx="20" cy="-5" rx="25" ry="12" fill="#FFFFFF" />
    <ellipse cx="-15" cy="-3" rx="20" ry="10" fill="#FFFFFF" />
  </g>
  
  <g transform="translate(1720, 680)" opacity="0.4">
    <ellipse cx="0" cy="0" rx="25" ry="12" fill="#FFFFFF" />
    <ellipse cx="15" cy="-4" rx="20" ry="10" fill="#FFFFFF" />
    <ellipse cx="-12" cy="-2" rx="18" ry="8" fill="#FFFFFF" />
  </g>
  
  <!-- 装饰山峰轮廓 -->
  <path d="M 0 750 L 200 650 L 400 700 L 600 620 L 800 680 L 1000 600 L 1200 660 L 1400 580 L 1600 640 L 1800 560 L 1920 620 L 1920 1080 L 0 1080 Z" fill="#2F4F4F" opacity="0.2" />
</svg>
