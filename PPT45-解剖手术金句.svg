<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 背景装饰 -->
  <defs>
    <radialGradient id="surgicalGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#E6F3FF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#CCE7FF;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#surgicalGradient)" />
  
  <!-- 手术刀装饰 -->
  <g transform="translate(300, 300)">
    <line x1="0" y1="0" x2="80" y2="0" stroke="#005A9E" stroke-width="6" />
    <polygon points="80,0 100,-5 100,5" fill="#005A9E" />
    <rect x="-20" y="-8" width="20" height="16" fill="#F5A623" rx="3" />
  </g>
  
  <g transform="translate(1620, 780)">
    <line x1="0" y1="0" x2="-80" y2="0" stroke="#005A9E" stroke-width="6" />
    <polygon points="-80,0 -100,-5 -100,5" fill="#005A9E" />
    <rect x="0" y="-8" width="20" height="16" fill="#F5A623" rx="3" />
  </g>
  
  <!-- 主要金句 -->
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="72" font-weight="bold" fill="#005A9E">
    我们不是在采访英雄，
  </text>
  
  <text x="960" y="580" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="72" font-weight="bold" fill="#F5A623">
    我们是在解剖一次完美的手术。
  </text>
  
  <!-- 装饰线条 -->
  <line x1="200" y1="700" x2="1720" y2="700" stroke="#005A9E" stroke-width="6" />
  <line x1="300" y1="720" x2="1620" y2="720" stroke="#F5A623" stroke-width="4" />
  
  <!-- 手术台装饰 -->
  <rect x="400" y="800" width="1120" height="100" fill="#005A9E" opacity="0.1" rx="15" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-style="italic" fill="#005A9E">
    STAR-E就是我们工程师的"手术流程单"
  </text>
  
  <!-- 精密仪器装饰 -->
  <circle cx="200" cy="600" r="30" fill="none" stroke="#005A9E" stroke-width="4" opacity="0.3" />
  <circle cx="200" cy="600" r="20" fill="none" stroke="#005A9E" stroke-width="2" opacity="0.5" />
  <circle cx="200" cy="600" r="10" fill="#005A9E" opacity="0.3" />
  
  <circle cx="1720" cy="450" r="25" fill="none" stroke="#F5A623" stroke-width="3" opacity="0.4" />
  <circle cx="1720" cy="450" r="15" fill="none" stroke="#F5A623" stroke-width="2" opacity="0.6" />
  <circle cx="1720" cy="450" r="8" fill="#F5A623" opacity="0.4" />
</svg>
