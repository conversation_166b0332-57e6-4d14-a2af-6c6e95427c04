<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    科学的"植入"系统：战法植入三步法
  </text>
  
  <!-- 第一步 -->
  <rect x="200" y="250" width="450" height="200" fill="#4CAF50" opacity="0.1" rx="20" />
  <circle cx="300" cy="300" r="30" fill="#4CAF50" />
  <text x="300" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">
    1
  </text>
  
  <!-- 沙盒/游戏手柄图标 -->
  <g transform="translate(380, 280)">
    <rect x="-25" y="-15" width="50" height="30" fill="#4CAF50" rx="15" />
    <circle cx="-15" cy="-5" r="4" fill="#FFFFFF" />
    <circle cx="15" cy="-5" r="4" fill="#FFFFFF" />
    <rect x="-8" y="5" width="16" height="6" fill="#FFFFFF" rx="3" />
  </g>
  
  <text x="425" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#4CAF50">
    第一步：最小化模拟
  </text>
  <text x="425" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#4CAF50">
    (Minimized Simulation)
  </text>
  <text x="425" y="350" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    目的：降低心理门槛，
  </text>
  <text x="425" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    消除行动恐惧。
  </text>
  
  <!-- 箭头1 -->
  <path d="M 670 350 L 730 350" stroke="#F5A623" stroke-width="6" marker-end="url(#arrowhead)" />
  
  <!-- 第二步 -->
  <rect x="750" y="250" width="450" height="200" fill="#005A9E" opacity="0.1" rx="20" />
  <circle cx="850" cy="300" r="30" fill="#005A9E" />
  <text x="850" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">
    2
  </text>
  
  <!-- 教练/陪练图标 -->
  <g transform="translate(930, 280)">
    <circle cx="0" cy="0" r="15" fill="#005A9E" />
    <circle cx="20" cy="0" r="12" fill="#F5A623" />
    <line x1="15" y1="0" x2="8" y2="0" stroke="#005A9E" stroke-width="2" />
  </g>
  
  <text x="975" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#005A9E">
    第二步：带教式通关
  </text>
  <text x="975" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#005A9E">
    (Coached Pass-Through)
  </text>
  <text x="975" y="350" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    目的：精准纠偏，在形成肌肉
  </text>
  <text x="975" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    记忆前，确保动作正确。
  </text>
  
  <!-- 箭头2 -->
  <path d="M 1220 350 L 1280 350" stroke="#F5A623" stroke-width="6" marker-end="url(#arrowhead)" />
  
  <!-- 第三步 -->
  <rect x="1300" y="250" width="450" height="200" fill="#F5A623" opacity="0.1" rx="20" />
  <circle cx="1400" cy="300" r="30" fill="#F5A623" />
  <text x="1400" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">
    3
  </text>
  
  <!-- 奖牌/认证图标 -->
  <g transform="translate(1480, 280)">
    <circle cx="0" cy="0" r="15" fill="#FFD700" />
    <polygon points="0,-8 6,0 0,8 -6,0" fill="#F5A623" />
    <rect x="-2" y="15" width="4" height="15" fill="#8B4513" />
  </g>
  
  <text x="1525" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
    第三步：实战中认证
  </text>
  <text x="1525" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#F5A623">
    (In-Action Certification)
  </text>
  <text x="1525" y="350" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    目的：连接训练与绩效，
  </text>
  <text x="1525" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    打通最后一公里。
  </text>
  
  <!-- 能力模拟舱概念 -->
  <rect x="200" y="500" width="1520" height="200" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="560" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    构建科学的"能力模拟舱"
  </text>
  
  <text x="960" y="620" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    从"零件练习"到"整体组装"，再到"实战认证"
  </text>
  <text x="960" y="660" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    确保每一位学员都能在真实战场上发挥作用
  </text>
  
  <!-- 装饰模拟舱 -->
  <g transform="translate(960, 800)">
    <rect x="-100" y="-30" width="200" height="60" fill="#4169E1" opacity="0.3" rx="30" />
    <circle cx="-60" cy="0" r="8" fill="#4CAF50" />
    <circle cx="-20" cy="0" r="8" fill="#F5A623" />
    <circle cx="20" cy="0" r="8" fill="#FF6B6B" />
    <circle cx="60" cy="0" r="8" fill="#9C27B0" />
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      能力模拟舱
    </text>
  </g>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#F5A623" />
    </marker>
  </defs>
</svg>
