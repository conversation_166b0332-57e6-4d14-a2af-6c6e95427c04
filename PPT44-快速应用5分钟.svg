<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 练习本背景 -->
  <defs>
    <pattern id="notebookLines" patternUnits="userSpaceOnUse" width="40" height="40">
      <rect width="40" height="40" fill="#F8F8FF" />
      <line x1="0" y1="20" x2="40" y2="20" stroke="#E0E0E0" stroke-width="1" />
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#notebookLines)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    快速应用 (5分钟)
  </text>
  
  <!-- 练习本装饰 -->
  <rect x="200" y="200" width="1520" height="650" fill="#FFFFFF" opacity="0.9" stroke="#005A9E" stroke-width="4" rx="20" />
  
  <!-- 螺旋装订效果 -->
  <circle cx="250" cy="250" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  <circle cx="250" cy="350" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  <circle cx="250" cy="450" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  <circle cx="250" cy="550" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  <circle cx="250" cy="650" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  <circle cx="250" cy="750" r="15" fill="none" stroke="#005A9E" stroke-width="3" />
  
  <!-- 任务内容 -->
  <text x="960" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    任务：
  </text>
  
  <text x="350" y="370" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 请翻开你们昨天的《经验传承失败复盘画布》
  </text>
  
  <text x="350" y="430" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 尝试用STAR-E的框架，重新梳理和定义那个失败的案例。
  </text>
  
  <text x="350" y="520" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    思考：
  </text>
  
  <text x="350" y="580" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    用了新框架后，你对那个"失败"的诊断，
  </text>
  <text x="350" y="620" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    是否有了全新的、更深刻的发现？
  </text>
  
  <!-- 时钟图标 -->
  <g transform="translate(1500, 400)">
    <circle cx="0" cy="0" r="60" fill="#F5A623" opacity="0.2" />
    <circle cx="0" cy="0" r="45" fill="none" stroke="#F5A623" stroke-width="6" />
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      5分钟
    </text>
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-30" stroke="#005A9E" stroke-width="4" />
    <line x1="0" y1="0" x2="20" y2="0" stroke="#005A9E" stroke-width="3" />
    <circle cx="0" cy="0" r="5" fill="#005A9E" />
  </g>
  
  <!-- STAR-E提醒 -->
  <rect x="1200" y="600" width="400" height="200" fill="#005A9E" opacity="0.1" rx="15" />
  <text x="1400" y="640" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#005A9E">
    STAR-E框架
  </text>
  <text x="1220" y="680" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
    S - 情景 (Situation)
  </text>
  <text x="1220" y="710" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
    T - 任务 (Task)
  </text>
  <text x="1220" y="740" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
    A - 行动 (Action)
  </text>
  <text x="1220" y="770" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
    R - 结果 (Result)
  </text>
  <text x="1220" y="800" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
    E - 萃取 (Extraction)
  </text>
</svg>
