<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 专注背景 -->
  <defs>
    <radialGradient id="focusGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#E6F3FF;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#CCE7FF;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#focusGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 巨大倒计时器 -->
  <g transform="translate(960, 540)">
    <!-- 外圈 -->
    <circle cx="0" cy="0" r="300" fill="#F5A623" opacity="0.05" />
    <circle cx="0" cy="0" r="250" fill="none" stroke="#F5A623" stroke-width="25" />
    
    <!-- 内圈 -->
    <circle cx="0" cy="0" r="200" fill="#005A9E" opacity="0.05" />
    <circle cx="0" cy="0" r="160" fill="none" stroke="#005A9E" stroke-width="20" />
    
    <!-- 时间显示 -->
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="140" font-weight="bold" fill="#F5A623">
      30:00
    </text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-140" stroke="#005A9E" stroke-width="15" />
    <line x1="0" y1="0" x2="90" y2="0" stroke="#005A9E" stroke-width="10" />
    <circle cx="0" cy="0" r="25" fill="#005A9E" />
    
    <!-- 主要刻度 -->
    <g stroke="#F5A623" stroke-width="6">
      <line x1="0" y1="-150" x2="0" y2="-135" />
      <line x1="150" y1="0" x2="135" y2="0" />
      <line x1="0" y1="150" x2="0" y2="135" />
      <line x1="-150" y1="0" x2="-135" y2="0" />
    </g>
    
    <!-- 次要刻度 -->
    <g stroke="#F5A623" stroke-width="4" opacity="0.7">
      <line x1="106" y1="-106" x2="100" y2="-100" />
      <line x1="106" y1="106" x2="100" y2="100" />
      <line x1="-106" y1="106" x2="-100" y2="100" />
      <line x1="-106" y1="-106" x2="-100" y2="-100" />
    </g>
    
    <!-- 分钟标记 -->
    <g stroke="#005A9E" stroke-width="3" opacity="0.6">
      <line x1="75" y1="-75" x2="70" y2="-70" />
      <line x1="75" y1="75" x2="70" y2="70" />
      <line x1="-75" y1="75" x2="-70" y2="70" />
      <line x1="-75" y1="-75" x2="-70" y2="-70" />
    </g>
  </g>
  
  <!-- 训练进行中提示 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    植入演练进行中...
  </text>
  
  <!-- 装饰训练图标 -->
  <g transform="translate(300, 300)" opacity="0.4">
    <!-- 教练 -->
    <circle cx="0" cy="0" r="20" fill="#005A9E" />
    <rect x="-15" y="20" width="30" height="35" fill="#005A9E" />
    <line x1="-20" y1="30" x2="-30" y2="35" stroke="#005A9E" stroke-width="3" />
    <line x1="20" y1="30" x2="30" y2="35" stroke="#005A9E" stroke-width="3" />
    
    <!-- 学员 -->
    <circle cx="60" cy="0" r="18" fill="#4CAF50" />
    <rect x="47" y="18" width="26" height="32" fill="#4CAF50" />
    <line x1="42" y1="28" x2="32" y2="33" stroke="#4CAF50" stroke-width="3" />
    <line x1="78" y1="28" x2="88" y2="33" stroke="#4CAF50" stroke-width="3" />
    
    <!-- 对话线 -->
    <path d="M 25 10 Q 35 5 45 10" stroke="#F5A623" stroke-width="2" fill="none" />
  </g>
  
  <g transform="translate(1620, 300)" opacity="0.4">
    <!-- 另一组 -->
    <circle cx="0" cy="0" r="18" fill="#4CAF50" />
    <rect x="-13" y="18" width="26" height="32" fill="#4CAF50" />
    <line x1="-18" y1="28" x2="-28" y2="33" stroke="#4CAF50" stroke-width="3" />
    <line x1="18" y1="28" x2="28" y2="33" stroke="#4CAF50" stroke-width="3" />
    
    <circle cx="60" cy="0" r="20" fill="#005A9E" />
    <rect x="45" y="20" width="30" height="35" fill="#005A9E" />
    <line x1="40" y1="30" x2="30" y2="35" stroke="#005A9E" stroke-width="3" />
    <line x1="80" y1="30" x2="90" y2="35" stroke="#005A9E" stroke-width="3" />
    
    <!-- 对话线 -->
    <path d="M 25 10 Q 35 5 45 10" stroke="#F5A623" stroke-width="2" fill="none" />
  </g>
  
  <!-- 底部激励文字 -->
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-style="italic" fill="#005A9E" opacity="0.7">
    真正的工程师，在传授技能的过程中诞生
  </text>
  
  <!-- 装饰音符 -->
  <g transform="translate(200, 800)" opacity="0.3">
    <circle cx="0" cy="0" r="6" fill="#F5A623" />
    <line x1="6" y1="0" x2="6" y2="-25" stroke="#F5A623" stroke-width="2" />
    <path d="M 6 -25 Q 15 -28 18 -20" stroke="#F5A623" stroke-width="2" fill="none" />
  </g>
  
  <g transform="translate(1720, 800)" opacity="0.3">
    <circle cx="0" cy="0" r="5" fill="#005A9E" />
    <line x1="5" y1="0" x2="5" y2="-20" stroke="#005A9E" stroke-width="2" />
    <path d="M 5 -20 Q 12 -22 15 -16" stroke="#005A9E" stroke-width="2" fill="none" />
  </g>
</svg>
