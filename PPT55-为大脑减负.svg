<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    设计哲学：为大脑"减负"
  </text>
  
  <!-- 超负荷大脑图 -->
  <g transform="translate(300, 350)">
    <!-- 大脑 -->
    <ellipse cx="0" cy="0" rx="100" ry="80" fill="#FFB6C1" opacity="0.8" />
    <path d="M -80 -40 Q 0 -80 80 -40 Q 100 0 80 40 Q 0 80 -80 40 Q -100 0 -80 -40" fill="#FF69B4" opacity="0.6" />
    
    <!-- 压力指示器 -->
    <rect x="-50" y="-120" width="100" height="20" fill="#FF0000" rx="10" />
    <text x="0" y="-105" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#FFFFFF">
      认知负荷 100%
    </text>
    
    <!-- 干扰信息 -->
    <g opacity="0.7">
      <!-- 电话 -->
      <rect x="-150" y="-50" width="30" height="20" fill="#333333" rx="5" />
      <text x="-135" y="-35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">📞</text>
      
      <!-- 邮件 -->
      <rect x="120" y="-60" width="25" height="18" fill="#005A9E" rx="3" />
      <text x="132" y="-48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" fill="#FFFFFF">✉</text>
      
      <!-- 客户抱怨 -->
      <circle cx="-120" cy="60" r="20" fill="#FF4500" />
      <text x="-120" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">😠</text>
      
      <!-- 任务 -->
      <rect x="100" y="50" width="35" height="25" fill="#F5A623" rx="5" />
      <text x="117" y="67" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10" fill="#FFFFFF">任务</text>
    </g>
    
    <!-- 箭头指向大脑 -->
    <path d="M -120 -30 L -90 -20" stroke="#FF0000" stroke-width="3" marker-end="url(#arrowhead)" />
    <path d="M 120 -40 L 90 -30" stroke="#FF0000" stroke-width="3" marker-end="url(#arrowhead)" />
    <path d="M -100 40 L -80 20" stroke="#FF0000" stroke-width="3" marker-end="url(#arrowhead)" />
    <path d="M 110 40 L 80 20" stroke="#FF0000" stroke-width="3" marker-end="url(#arrowhead)" />
  </g>
  
  <!-- 一线战场的真相 -->
  <rect x="700" y="250" width="1000" height="200" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1200" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    一线战场的真相：
  </text>
  
  <text x="750" y="350" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    • 高压力 (High Pressure)
  </text>
  <text x="750" y="390" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    • 高干扰 (High Distraction)
  </text>
  <text x="750" y="430" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    • 有限的"认知带宽" (Limited Cognitive Bandwidth)
  </text>
  
  <!-- 我们的设计目标 -->
  <rect x="200" y="550" width="1520" height="200" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="600" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    我们的设计目标：
  </text>
  
  <text x="960" y="660" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    不是增加信息，而是降低负荷。
  </text>
  <text x="960" y="710" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    不是要求思考，而是辅助决策。
  </text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#FF0000" />
    </marker>
  </defs>
</svg>
