<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 工程美学背景 -->
  <defs>
    <radialGradient id="forgeGradient" cx="30%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#F7931E;stop-opacity:0.08" />
      <stop offset="100%" style="stop-color:#005A9E;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#forgeGradient)" />
  
  <!-- 页面标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    模块三：工具再造
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44" fill="#F5A623">
    将"知识矿石"锻造成"战法武器"
  </text>
  
  <!-- 锻造工坊装饰 -->
  <g transform="translate(300, 350)">
    <!-- 锻造炉 -->
    <rect x="0" y="0" width="120" height="80" fill="#8B4513" rx="10" />
    <rect x="10" y="10" width="100" height="60" fill="#FF6B35" opacity="0.7" rx="5" />
    
    <!-- 火焰 -->
    <path d="M 30 10 Q 35 0 40 10 Q 45 0 50 10 Q 55 0 60 10" stroke="#FF4500" stroke-width="4" fill="none" />
    <path d="M 70 10 Q 75 0 80 10 Q 85 0 90 10" stroke="#FF4500" stroke-width="4" fill="none" />
    
    <!-- 锤子 -->
    <rect x="150" y="30" width="60" height="20" fill="#696969" rx="5" />
    <line x1="210" y1="40" x2="280" y2="40" stroke="#8B4513" stroke-width="8" />
    
    <!-- 铁砧 -->
    <rect x="0" y="100" width="80" height="30" fill="#696969" rx="5" />
    <rect x="-10" y="130" width="100" height="20" fill="#2F4F4F" rx="3" />
  </g>
  
  <!-- 本节核心议题 -->
  <rect x="600" y="320" width="1100" height="500" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1150" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    本节核心议题：
  </text>
  
  <!-- 议题列表 -->
  <text x="650" y="450" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    设计哲学：
  </text>
  <text x="850" y="450" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "免思考"原则
  </text>
  
  <text x="650" y="520" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    核心框架：
  </text>
  <text x="850" y="520" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "战法工具化三件套"
  </text>
  
  <text x="650" y="590" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    实战工坊：
  </text>
  <text x="850" y="590" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    锻造你的第一份"清单武器"
  </text>
  
  <text x="650" y="660" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    集体智慧：
  </text>
  <text x="850" y="660" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "画廊漫步"与迭代升级
  </text>
  
  <!-- 装饰齿轮 -->
  <g transform="translate(1500, 500)">
    <circle cx="0" cy="0" r="60" fill="none" stroke="#F5A623" stroke-width="8" opacity="0.4" />
    <circle cx="0" cy="0" r="40" fill="none" stroke="#F5A623" stroke-width="6" opacity="0.6" />
    <circle cx="0" cy="0" r="20" fill="#F5A623" opacity="0.3" />
    <!-- 齿轮齿 -->
    <rect x="-4" y="-68" width="8" height="16" fill="#F5A623" opacity="0.4" />
    <rect x="-4" y="52" width="8" height="16" fill="#F5A623" opacity="0.4" />
    <rect x="52" y="-4" width="16" height="8" fill="#F5A623" opacity="0.4" />
    <rect x="-68" y="-4" width="16" height="8" fill="#F5A623" opacity="0.4" />
  </g>
</svg>
