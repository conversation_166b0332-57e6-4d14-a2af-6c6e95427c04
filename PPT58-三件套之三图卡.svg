<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    三件套之三：图卡 (Flashcard)
  </text>
  
  <!-- 弹药/记忆卡图标 -->
  <g transform="translate(400, 400)">
    <!-- 弹药盒 -->
    <rect x="-80" y="-40" width="160" height="80" fill="#8B4513" rx="10" />
    <rect x="-70" y="-30" width="140" height="60" fill="#D2691E" rx="8" />
    
    <!-- 弹药/卡片 -->
    <rect x="-60" y="-50" width="25" height="40" fill="#FFD700" rx="3" />
    <rect x="-25" y="-50" width="25" height="40" fill="#FFD700" rx="3" />
    <rect x="10" y="-50" width="25" height="40" fill="#FFD700" rx="3" />
    <rect x="45" y="-50" width="25" height="40" fill="#FFD700" rx="3" />
    
    <!-- 卡片上的文字 -->
    <text x="-47" y="-25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" fill="#333333">心法</text>
    <text x="-12" y="-25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" fill="#333333">要点</text>
    <text x="22" y="-25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" fill="#333333">口诀</text>
    <text x="57" y="-25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8" fill="#333333">技巧</text>
    
    <!-- 闪光效果 -->
    <g opacity="0.6">
      <circle cx="-47" cy="-60" r="3" fill="#FFFF00" />
      <circle cx="22" cy="-60" r="2" fill="#FFFF00" />
      <circle cx="70" cy="-20" r="2" fill="#FFFF00" />
      <circle cx="-90" cy="10" r="3" fill="#FFFF00" />
    </g>
    
    <!-- 激活效果 -->
    <g transform="translate(0, 60)" opacity="0.7">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#FF4500">
        ⚡ 瞬间激活 ⚡
      </text>
    </g>
  </g>
  
  <!-- 核心作用 -->
  <rect x="700" y="250" width="1000" height="100" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1200" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    核心作用：激活记忆的"弹药"
  </text>
  
  <!-- 适用场景 -->
  <rect x="700" y="400" width="1000" height="350" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="1200" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    适用场景：
  </text>
  
  <text x="750" y="510" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 需要瞬间记起、脱口而出的关键知识点
  </text>
  
  <text x="750" y="570" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 压力下的"即时提醒"和"心法口诀"
  </text>
  
  <text x="750" y="630" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#666666">
    (如：销售话术要点、应急处理口诀)
  </text>
  
  <!-- 装饰记忆卡片 -->
  <g transform="translate(200, 650)">
    <!-- 卡片堆叠效果 -->
    <rect x="5" y="5" width="120" height="80" fill="#CCCCCC" rx="8" opacity="0.5" />
    <rect x="2" y="2" width="120" height="80" fill="#DDDDDD" rx="8" opacity="0.7" />
    <rect x="0" y="0" width="120" height="80" fill="#FFFFFF" stroke="#005A9E" stroke-width="3" rx="8" />
    
    <!-- 卡片内容 -->
    <text x="60" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" fill="#F5A623">
      核心心法
    </text>
    <line x1="15" y1="35" x2="105" y2="35" stroke="#005A9E" stroke-width="1" />
    <text x="60" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#333333">
      先听后说
    </text>
    <text x="60" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#333333">
      问题导向
    </text>
  </g>
</svg>
