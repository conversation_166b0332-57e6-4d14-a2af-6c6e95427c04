<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    第二把钥匙：过程追问
  </text>
  
  <!-- 目的 -->
  <rect x="200" y="200" width="1520" height="100" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="260" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    目的：最简单也最有效的"地毯式搜索"
  </text>
  
  <!-- 钥匙图标 -->
  <g transform="translate(300, 400)">
    <rect x="0" y="-10" width="120" height="20" fill="#005A9E" rx="10" />
    <circle cx="130" cy="0" r="30" fill="#005A9E" />
    <circle cx="130" cy="0" r="20" fill="#FFFFFF" />
    <rect x="120" y="-5" width="20" height="3" fill="#005A9E" />
    <rect x="120" y="2" width="15" height="3" fill="#005A9E" />
    <rect x="120" y="7" width="10" height="3" fill="#005A9E" />
    
    <!-- 搜索雷达效果 -->
    <circle cx="130" cy="0" r="50" fill="none" stroke="#005A9E" stroke-width="2" opacity="0.3" />
    <circle cx="130" cy="0" r="70" fill="none" stroke="#005A9E" stroke-width="1" opacity="0.2" />
  </g>
  
  <!-- 核心句式 -->
  <rect x="600" y="350" width="1100" height="150" fill="#FFFFFF" opacity="0.9" stroke="#005A9E" stroke-width="4" rx="20" />
  <text x="1150" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    核心句式：
  </text>
  <text x="1150" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#F5A623">
    "然后呢？"
  </text>
  
  <!-- 话术范例 -->
  <rect x="200" y="550" width="1520" height="300" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="600" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    话术范例：
  </text>
  
  <rect x="250" y="630" width="1420" height="200" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="960" y="680" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    当对方说完一步，你就追问"然后呢？"
  </text>
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    像一个三岁的孩子，保持你的好奇心，
  </text>
  <text x="960" y="760" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    直到他把一个完整的行为序列全部吐出来。
  </text>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-style="italic" fill="#005A9E">
    这是最简单也最有效的"地毯式搜索"。
  </text>
  
  <!-- 三岁孩子装饰 -->
  <g transform="translate(1500, 400)">
    <circle cx="0" cy="0" r="25" fill="#F5A623" opacity="0.7" />
    <circle cx="-8" cy="-5" r="3" fill="#FFFFFF" />
    <circle cx="8" cy="-5" r="3" fill="#FFFFFF" />
    <ellipse cx="0" cy="5" rx="8" ry="4" fill="#FFFFFF" />
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#F5A623">
      好奇心
    </text>
  </g>
</svg>
