<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 温暖午餐背景 -->
  <defs>
    <radialGradient id="lunchGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#FFF8DC;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#F5DEB3;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#DEB887;stop-opacity:0.2" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#lunchGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#F5A623">
    午餐时间
  </text>
  
  <!-- 餐具装饰 -->
  <g transform="translate(400, 400)">
    <!-- 盘子 -->
    <circle cx="0" cy="0" r="80" fill="#FFFFFF" stroke="#F5A623" stroke-width="6" />
    <circle cx="0" cy="0" r="60" fill="#F5DEB3" opacity="0.3" />
    
    <!-- 叉子 -->
    <line x1="-120" y1="-20" x2="-120" y2="-80" stroke="#005A9E" stroke-width="6" />
    <line x1="-130" y1="-80" x2="-110" y2="-80" stroke="#005A9E" stroke-width="4" />
    <line x1="-125" y1="-85" x2="-125" y2="-75" stroke="#005A9E" stroke-width="2" />
    <line x1="-120" y1="-85" x2="-120" y2="-75" stroke="#005A9E" stroke-width="2" />
    <line x1="-115" y1="-85" x2="-115" y2="-75" stroke="#005A9E" stroke-width="2" />
    
    <!-- 刀子 -->
    <line x1="120" y1="-20" x2="120" y2="-80" stroke="#005A9E" stroke-width="6" />
    <path d="M 115 -80 L 125 -80 L 120 -85 Z" fill="#005A9E" />
  </g>
  
  <g transform="translate(1520, 400)">
    <!-- 杯子 -->
    <rect x="-30" y="-40" width="60" height="80" fill="#FFFFFF" stroke="#F5A623" stroke-width="4" rx="5" />
    <rect x="-25" y="-35" width="50" height="60" fill="#F5DEB3" opacity="0.4" />
    <rect x="30" y="-10" width="20" height="8" fill="#F5A623" rx="4" />
    
    <!-- 蒸汽 -->
    <path d="M -10 -50 Q -5 -60 0 -50 Q 5 -60 10 -50" stroke="#F5A623" stroke-width="3" fill="none" opacity="0.6" />
    <path d="M -15 -55 Q -10 -65 -5 -55 Q 0 -65 5 -55" stroke="#F5A623" stroke-width="2" fill="none" opacity="0.4" />
  </g>
  
  <!-- 时间信息 -->
  <rect x="300" y="600" width="1320" height="200" fill="#FFFFFF" opacity="0.9" rx="30" />
  <text x="960" y="680" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    12:00 - 13:30
  </text>
  <text x="960" y="740" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#333333">
    享受美食，放松身心
  </text>
  
  <!-- 下午预告 -->
  <rect x="400" y="850" width="1120" height="100" fill="#F5A623" opacity="0.2" rx="20" />
  <text x="960" y="910" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-style="italic" fill="#005A9E">
    下午13:30，我们将开始"工具再造"的精彩旅程！
  </text>
  
  <!-- 装饰食物图标 -->
  <circle cx="200" cy="600" r="15" fill="#F5A623" opacity="0.6" />
  <circle cx="220" cy="620" r="12" fill="#F5A623" opacity="0.5" />
  <circle cx="240" cy="640" r="10" fill="#F5A623" opacity="0.4" />
  
  <circle cx="1720" cy="700" r="15" fill="#005A9E" opacity="0.6" />
  <circle cx="1700" cy="720" r="12" fill="#005A9E" opacity="0.5" />
  <circle cx="1680" cy="740" r="10" fill="#005A9E" opacity="0.4" />
</svg>
