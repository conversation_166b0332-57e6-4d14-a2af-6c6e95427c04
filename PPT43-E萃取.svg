<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    (E) Extraction 萃取
  </text>
  
  <!-- 核心目标 -->
  <rect x="200" y="200" width="1520" height="120" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    核心目标：点石成金的最后一步
  </text>
  
  <!-- 点石成金图标 -->
  <g transform="translate(300, 450)">
    <!-- 石头 -->
    <ellipse cx="-50" cy="0" rx="30" ry="25" fill="#999999" />
    <ellipse cx="-50" cy="0" rx="20" ry="15" fill="#CCCCCC" />
    
    <!-- 魔法棒 -->
    <line x1="-10" y1="-30" x2="10" y2="30" stroke="#F5A623" stroke-width="6" />
    <polygon points="5,-35 15,-25 10,-30" fill="#FFD700" />
    <circle cx="10" cy="-30" r="5" fill="#FFD700" />
    
    <!-- 箭头 -->
    <path d="M 30 0 L 70 0" stroke="#005A9E" stroke-width="6" marker-end="url(#arrowhead)" />
    
    <!-- 金子 -->
    <ellipse cx="100" cy="0" rx="30" ry="25" fill="#FFD700" />
    <ellipse cx="100" cy="0" rx="20" ry="15" fill="#FFA500" />
    
    <!-- 闪光效果 -->
    <circle cx="85" cy="-15" r="3" fill="#FFFF00" />
    <circle cx="115" cy="15" r="2" fill="#FFFF00" />
    <circle cx="120" cy="-10" r="2" fill="#FFFF00" />
  </g>
  
  <!-- 关键提问范例 -->
  <rect x="600" y="350" width="1100" height="400" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1150" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    关键提问范例：
  </text>
  
  <rect x="650" y="450" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="490" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "回顾整个过程，你认为做对的
  </text>
  <text x="1150" y="530" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    最关键的一件事是什么？"
  </text>
  
  <rect x="650" y="590" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="630" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "如果让你提炼成一条口诀教给新人，
  </text>
  <text x="1150" y="670" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    会是什么？"
  </text>
  
  <!-- 强调说明 -->
  <rect x="200" y="800" width="1520" height="100" fill="#F5A623" opacity="0.2" rx="15" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    当整个故事讲完后，我们才和专家一起，进行复盘和提炼
  </text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#005A9E" />
    </marker>
  </defs>
</svg>
