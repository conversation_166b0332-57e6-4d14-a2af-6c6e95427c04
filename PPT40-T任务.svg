<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#F5A623">
    (T) Task 任务
  </text>
  
  <!-- 核心目标 -->
  <rect x="200" y="200" width="1520" height="120" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#F5A623">
    核心目标：找到那个必须被射中的"最小化的靶心"
  </text>
  
  <!-- 靶心图标 -->
  <g transform="translate(300, 450)">
    <circle cx="0" cy="0" r="80" fill="#F5A623" opacity="0.2" />
    <circle cx="0" cy="0" r="60" fill="#F5A623" opacity="0.4" />
    <circle cx="0" cy="0" r="40" fill="#F5A623" opacity="0.6" />
    <circle cx="0" cy="0" r="20" fill="#F5A623" />
    <circle cx="0" cy="0" r="5" fill="#FFFFFF" />
    
    <!-- 箭头指向靶心 -->
    <path d="M -150 -50 L -30 -10" stroke="#005A9E" stroke-width="6" marker-end="url(#arrowhead)" />
    <polygon points="-160,-60 -140,-40 -150,-50" fill="#005A9E" />
  </g>
  
  <!-- 关键提问范例 -->
  <rect x="600" y="350" width="1100" height="400" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="1150" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#F5A623">
    关键提问范例：
  </text>
  
  <rect x="650" y="450" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="490" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    不要问"你目标是啥"
  </text>
  <text x="1150" y="530" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    要问"如果那次沟通只能完成一件事，"
  </text>
  
  <rect x="650" y="590" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="630" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    那件必须拿下的事是什么？"
  </text>
  
  <!-- 装饰元素 -->
  <rect x="200" y="800" width="200" height="150" fill="#F5A623" opacity="0.2" rx="10" />
  <text x="300" y="880" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
    最小化靶心
  </text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#005A9E" />
    </marker>
  </defs>
</svg>
