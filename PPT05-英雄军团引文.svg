<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 全黑背景 -->
  <rect width="100%" height="100%" fill="#000000" />
  
  <!-- 装饰弧线 - 左上角 (白色) -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 (白色) -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 引号装饰 -->
  <text x="400" y="400" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="200" fill="#F5A623" opacity="0.3">
    "
  </text>
  
  <!-- 主要引文 -->
  <text x="960" y="500" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#FFFFFF">
    我们需要的，不是一个超级英雄，
  </text>
  
  <text x="960" y="600" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#FFFFFF">
    而是一支英雄军团。
  </text>
  
  <!-- 引号装饰 -->
  <text x="1420" y="700" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="200" fill="#F5A623" opacity="0.3">
    "
  </text>
  
  <!-- 署名 -->
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" fill="#F5A623">
    —— 业务副总裁
  </text>
  
  <!-- 装饰线条 -->
  <line x1="560" y1="820" x2="1360" y2="820" stroke="#F5A623" stroke-width="4" />
</svg>
