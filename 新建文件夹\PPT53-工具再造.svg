<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 工业锻造背景 -->
  <defs>
    <radialGradient id="forgeGradient" cx="30%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#F7931E;stop-opacity:0.08" />
      <stop offset="100%" style="stop-color:#005A9E;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#forgeGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    模块四：工具再造
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" fill="#F5A623">
    从"矿石"到"武器"的锻造之术
  </text>
  
  <!-- 锻造炉装饰 -->
  <g transform="translate(300, 450)">
    <!-- 炉子 -->
    <rect x="0" y="0" width="120" height="80" fill="#8B4513" rx="10" />
    <rect x="10" y="10" width="100" height="60" fill="#FF6B35" opacity="0.6" rx="5" />
    
    <!-- 火焰 -->
    <path d="M 30 10 Q 35 0 40 10 Q 45 0 50 10 Q 55 0 60 10" stroke="#FF4500" stroke-width="4" fill="none" />
    <path d="M 70 10 Q 75 0 80 10 Q 85 0 90 10" stroke="#FF4500" stroke-width="4" fill="none" />
    
    <!-- 锤子 -->
    <rect x="150" y="30" width="60" height="20" fill="#696969" rx="5" />
    <line x1="210" y1="40" x2="280" y2="40" stroke="#8B4513" stroke-width="8" />
    
    <!-- 铁砧 -->
    <rect x="0" y="100" width="80" height="30" fill="#696969" rx="5" />
    <rect x="-10" y="130" width="100" height="20" fill="#2F4F4F" rx="3" />
  </g>
  
  <!-- 转化过程 -->
  <g transform="translate(960, 450)">
    <!-- 矿石 -->
    <ellipse cx="-200" cy="0" rx="40" ry="30" fill="#8B7355" />
    <ellipse cx="-200" cy="0" rx="25" ry="18" fill="#A0522D" />
    <text x="-200" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      原始经验
    </text>
    
    <!-- 箭头1 -->
    <path d="M -140 0 L -80 0" stroke="#F5A623" stroke-width="8" marker-end="url(#arrowhead)" />
    <text x="-110" y="-20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#F5A623">
      萃取
    </text>
    
    <!-- 精炼材料 -->
    <rect x="-60" y="-20" width="40" height="40" fill="#FFD700" rx="5" />
    <text x="-40" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      核心要素
    </text>
    
    <!-- 箭头2 -->
    <path d="M 0 0 L 60 0" stroke="#005A9E" stroke-width="8" marker-end="url(#arrowhead2)" />
    <text x="30" y="-20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#005A9E">
      锻造
    </text>
    
    <!-- 成品工具 -->
    <g transform="translate(120, 0)">
      <rect x="-30" y="-10" width="60" height="20" fill="#C0C0C0" rx="3" />
      <circle cx="35" cy="0" r="15" fill="#C0C0C0" />
      <circle cx="35" cy="0" r="8" fill="#FFFFFF" />
    </g>
    <text x="120" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      实用工具
    </text>
  </g>
  
  <!-- 三大武器类型 -->
  <rect x="200" y="650" width="1520" height="250" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="700" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    三大武器类型：
  </text>
  
  <rect x="250" y="730" width="450" height="150" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="475" y="770" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
    Checklist
  </text>
  <text x="475" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    检查清单
  </text>
  <text x="475" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    确保不遗漏
  </text>
  <text x="475" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    怕"想不全"
  </text>
  
  <rect x="735" y="730" width="450" height="150" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="960" y="770" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#005A9E">
    Template
  </text>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    标准模板
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    提升效率
  </text>
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    怕"想不快"
  </text>
  
  <rect x="1220" y="730" width="450" height="150" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1445" y="770" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
    Flashcard
  </text>
  <text x="1445" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
    记忆卡片
  </text>
  <text x="1445" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    强化关键记忆
  </text>
  <text x="1445" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#666666">
    怕"想不起"
  </text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#F5A623" />
    </marker>
    <marker id="arrowhead2" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#005A9E" />
    </marker>
  </defs>
</svg>
