<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    第二步：带教式通关 (Coached Pass-Through)
  </text>
  
  <!-- 核心理念 -->
  <rect x="200" y="200" width="1520" height="120" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    核心理念：
  </text>
  <text x="960" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    精准纠偏，在形成肌肉记忆前，确保动作正确
  </text>
  
  <!-- 教练陪练场景 -->
  <g transform="translate(300, 450)">
    <!-- 教练 -->
    <circle cx="0" cy="0" r="35" fill="#005A9E" />
    <rect x="-25" y="35" width="50" height="60" fill="#005A9E" />
    <line x1="-35" y1="50" x2="-55" y2="60" stroke="#005A9E" stroke-width="5" />
    <line x1="35" y1="50" x2="55" y2="60" stroke="#005A9E" stroke-width="5" />
    
    <!-- 教练帽子 -->
    <rect x="-20" y="-45" width="40" height="10" fill="#F5A623" rx="5" />
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      教练
    </text>
    
    <!-- 学员 -->
    <circle cx="150" cy="0" r="30" fill="#4CAF50" />
    <rect x="125" y="30" width="50" height="55" fill="#4CAF50" />
    <line x1="110" y1="45" x2="90" y2="55" stroke="#4CAF50" stroke-width="4" />
    <line x1="190" y1="45" x2="210" y2="55" stroke="#4CAF50" stroke-width="4" />
    <text x="150" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#4CAF50">
      学员
    </text>
    
    <!-- 对话气泡 -->
    <ellipse cx="75" cy="-50" rx="60" ry="30" fill="#FFFFFF" stroke="#005A9E" stroke-width="2" />
    <polygon points="45,-35 55,-25 35,-25" fill="#FFFFFF" stroke="#005A9E" stroke-width="2" />
    <text x="75" y="-45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#005A9E">
      很好！但这里可以
    </text>
    <text x="75" y="-30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#005A9E">
      更自然一些...
    </text>
  </g>
  
  <!-- 四个关键技术 -->
  <rect x="600" y="350" width="1100" height="400" fill="#005A9E" opacity="0.05" rx="20" />
  <text x="1150" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    四个关键技术：
  </text>
  
  <!-- 技术1 -->
  <g transform="translate(650, 450)">
    <circle cx="0" cy="0" r="15" fill="#F5A623" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">
      1
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      即时反馈
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      动作一结束，立即给出具体的改进建议
    </text>
  </g>
  
  <!-- 技术2 -->
  <g transform="translate(650, 520)">
    <circle cx="0" cy="0" r="15" fill="#F5A623" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">
      2
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      示范纠正
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      不只说"错了"，要演示"对的"是什么样
    </text>
  </g>
  
  <!-- 技术3 -->
  <g transform="translate(650, 590)">
    <circle cx="0" cy="0" r="15" fill="#F5A623" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">
      3
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      重复练习
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      同一个动作，练到自然流畅为止
    </text>
  </g>
  
  <!-- 技术4 -->
  <g transform="translate(650, 660)">
    <circle cx="0" cy="0" r="15" fill="#F5A623" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">
      4
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      渐进加压
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      从简单场景到复杂场景，逐步提升难度
    </text>
  </g>
  
  <!-- 底部关键提醒 -->
  <rect x="200" y="800" width="1520" height="120" fill="#FFD700" opacity="0.1" rx="20" />
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    关键提醒：
  </text>
  <text x="960" y="890" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    这一步的目标是"做对"，确保每个动作都符合标准！
  </text>
  
  <!-- 装饰精准度表 -->
  <g transform="translate(1600, 500)">
    <circle cx="0" cy="0" r="40" fill="none" stroke="#005A9E" stroke-width="4" />
    <circle cx="0" cy="0" r="25" fill="none" stroke="#F5A623" stroke-width="3" />
    <circle cx="0" cy="0" r="10" fill="#4CAF50" />
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#005A9E">
      精准纠偏
    </text>
  </g>
</svg>
