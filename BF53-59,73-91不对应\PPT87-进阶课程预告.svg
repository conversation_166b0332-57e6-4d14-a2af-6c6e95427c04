<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 未来感背景 -->
  <defs>
    <radialGradient id="futureGradient" cx="30%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#7B68EE;stop-opacity:0.08" />
      <stop offset="100%" style="stop-color:#005A9E;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#futureGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    进阶课程预告
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="210" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#F5A623">
    从"战法工程师"到"战法架构师"的进阶之路
  </text>
  
  <!-- 进阶路径图 -->
  <g transform="translate(200, 350)">
    <!-- 当前阶段 -->
    <rect x="0" y="0" width="200" height="100" fill="#4CAF50" rx="20" />
    <text x="100" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
      战法工程师
    </text>
    <text x="100" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#FFFFFF">
      (已完成)
    </text>
    
    <!-- 箭头1 -->
    <path d="M 220 50 L 280 50" stroke="#F5A623" stroke-width="6" marker-end="url(#arrowhead)" />
    
    <!-- 进阶阶段1 -->
    <rect x="300" y="0" width="200" height="100" fill="#005A9E" rx="20" />
    <text x="400" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#FFFFFF">
      战法系统师
    </text>
    <text x="400" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#FFFFFF">
      System Designer
    </text>
    <text x="400" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">
      构建完整方法论体系
    </text>
    
    <!-- 箭头2 -->
    <path d="M 520 50 L 580 50" stroke="#F5A623" stroke-width="6" marker-end="url(#arrowhead)" />
    
    <!-- 进阶阶段2 -->
    <rect x="600" y="0" width="200" height="100" fill="#9C27B0" rx="20" />
    <text x="700" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#FFFFFF">
      战法传承师
    </text>
    <text x="700" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#FFFFFF">
      Legacy Master
    </text>
    <text x="700" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">
      培养下一代工程师
    </text>
    
    <!-- 箭头3 -->
    <path d="M 820 50 L 880 50" stroke="#F5A623" stroke-width="6" marker-end="url(#arrowhead)" />
    
    <!-- 最高阶段 -->
    <rect x="900" y="0" width="200" height="100" fill="#FFD700" rx="20" />
    <text x="1000" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#8B4513">
      战法架构师
    </text>
    <text x="1000" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#8B4513">
      Strategic Architect
    </text>
    <text x="1000" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#8B4513">
      设计组织级战法体系
    </text>
  </g>
  
  <!-- 即将推出的课程 -->
  <rect x="200" y="500" width="1520" height="400" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="550" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    即将推出：《战法系统师》认证课程
  </text>
  
  <!-- 课程亮点 -->
  <g transform="translate(300, 600)">
    <circle cx="0" cy="0" r="15" fill="#4CAF50" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">
      1
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#4CAF50">
      系统化方法论构建
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      学会构建完整的、可复制的方法论体系
    </text>
  </g>
  
  <g transform="translate(300, 680)">
    <circle cx="0" cy="0" r="15" fill="#F5A623" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">
      2
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      跨部门战法整合
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      掌握不同业务线战法的整合与协同技术
    </text>
  </g>
  
  <g transform="translate(300, 760)">
    <circle cx="0" cy="0" r="15" fill="#9C27B0" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">
      3
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#9C27B0">
      组织级能力建设
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      从个人能力提升到组织能力建设的转变
    </text>
  </g>
  
  <g transform="translate(300, 840)">
    <circle cx="0" cy="0" r="15" fill="#00BCD4" />
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">
      4
    </text>
    <text x="30" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#00BCD4">
      数字化战法平台
    </text>
    <text x="30" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      利用技术手段构建智能化的战法管理平台
    </text>
  </g>
  
  <!-- 课程信息 -->
  <g transform="translate(1200, 650)">
    <rect x="0" y="0" width="400" height="200" fill="#F5A623" opacity="0.1" rx="15" />
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      课程信息
    </text>
    
    <text x="20" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" fill="#333333">
      📅 开课时间：2024年Q4
    </text>
    <text x="20" y="85" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" fill="#333333">
      ⏰ 课程时长：5天4夜
    </text>
    <text x="20" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" fill="#333333">
      👥 招生人数：限20人
    </text>
    <text x="20" y="135" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" fill="#333333">
      🎯 报名条件：完成战法工程师认证
    </text>
    <text x="20" y="160" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#F5A623">
      🎁 早鸟优惠：前10名享8折优惠
    </text>
  </g>
  
  <!-- 装饰火箭 -->
  <g transform="translate(1650, 400)">
    <!-- 火箭主体 -->
    <polygon points="0,0 15,60 -15,60" fill="#005A9E" />
    <polygon points="0,0 8,15 -8,15" fill="#F5A623" />
    <rect x="-8" y="45" width="16" height="10" fill="#FFFFFF" />
    
    <!-- 火焰 -->
    <path d="M -8 60 Q -12 70 -6 80 Q 0 75 0 85 Q 0 75 6 80 Q 12 70 8 60" fill="#FF4500" opacity="0.8" />
    
    <!-- 轨道 -->
    <path d="M 0 60 Q -50 30 -100 0" stroke="#F5A623" stroke-width="3" fill="none" stroke-dasharray="8,4" />
    <circle cx="-100" cy="0" r="6" fill="#FFD700" />
    
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14" fill="#005A9E">
      持续进阶
    </text>
  </g>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#F5A623" />
    </marker>
  </defs>
</svg>
