<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 羊皮纸背景 -->
  <defs>
    <radialGradient id="parchmentGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#FFF8DC;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#F5DEB3;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#DEB887;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#parchmentGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#8B4513" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#8B4513" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#8B4513" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#8B4513" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#8B4513">
    个人行动承诺书
  </text>
  
  <!-- 承诺书主体 -->
  <rect x="300" y="200" width="1320" height="650" fill="#FFFFFF" opacity="0.9" stroke="#8B4513" stroke-width="4" rx="20" />
  
  <!-- 标题装饰 -->
  <g transform="translate(960, 250)">
    <circle cx="0" cy="0" r="30" fill="#FFD700" />
    <polygon points="0,-15 10,0 0,15 -10,0" fill="#8B4513" />
  </g>
  
  <!-- 承诺内容 -->
  <text x="960" y="320" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#005A9E">
    我，_________________，郑重承诺：
  </text>
  
  <!-- 承诺条款 -->
  <g transform="translate(400, 380)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#8B4513">
      一、工具完善承诺：
    </text>
    <text x="30" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
      在30天内，我将完善我的清单工具至V2.0版本，并在至少3个真实场景中测试验证。
    </text>
  </g>
  
  <g transform="translate(400, 480)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#8B4513">
      二、传授技能承诺：
    </text>
    <text x="30" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
      在60天内，我将运用"三步植入法"，成功培训至少2名同事掌握我的工具。
    </text>
  </g>
  
  <g transform="translate(400, 580)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#8B4513">
      三、成果验证承诺：
    </text>
    <text x="30" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
      在90天内，我将量化工具使用效果，形成可复制的最佳实践案例。
    </text>
  </g>
  
  <g transform="translate(400, 680)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#8B4513">
      四、持续改进承诺：
    </text>
    <text x="30" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22" fill="#333333">
      我将持续收集反馈，不断迭代优化，真正成为一名"战法工程师"。
    </text>
  </g>
  
  <!-- 签名区域 -->
  <g transform="translate(1200, 750)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#8B4513">
      承诺人签名：_________________
    </text>
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#8B4513">
      日期：_________________
    </text>
  </g>
  
  <!-- 底部印章装饰 -->
  <g transform="translate(500, 780)">
    <circle cx="0" cy="0" r="40" fill="#FF6B6B" opacity="0.7" />
    <circle cx="0" cy="0" r="30" fill="none" stroke="#8B0000" stroke-width="3" />
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" fill="#8B0000">
      战法
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" font-weight="bold" fill="#8B0000">
      工程师
    </text>
  </g>
  
  <!-- 装饰羽毛笔 -->
  <g transform="translate(1500, 780)">
    <!-- 笔杆 -->
    <line x1="0" y1="0" x2="60" y2="-30" stroke="#8B4513" stroke-width="4" />
    <!-- 笔尖 -->
    <polygon points="60,-30 65,-28 62,-35" fill="#C0C0C0" />
    <!-- 羽毛 -->
    <path d="M 0 0 Q -10 -15 -5 -30 Q 0 -35 5 -30 Q 10 -15 0 0" fill="#4169E1" opacity="0.7" />
    <path d="M -2 -5 Q -8 -12 -3 -20" stroke="#FFFFFF" stroke-width="1" fill="none" />
    <path d="M 2 -8 Q 8 -15 3 -23" stroke="#FFFFFF" stroke-width="1" fill="none" />
  </g>
  
  <!-- 装饰边框花纹 -->
  <g transform="translate(320, 220)" opacity="0.6">
    <path d="M 0 0 Q 10 5 20 0 Q 30 -5 40 0" stroke="#8B4513" stroke-width="2" fill="none" />
    <circle cx="20" cy="0" r="3" fill="#FFD700" />
  </g>
  
  <g transform="translate(1560, 220)" opacity="0.6">
    <path d="M 0 0 Q -10 5 -20 0 Q -30 -5 -40 0" stroke="#8B4513" stroke-width="2" fill="none" />
    <circle cx="-20" cy="0" r="3" fill="#FFD700" />
  </g>
</svg>
