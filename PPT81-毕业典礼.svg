<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 庆典背景 -->
  <defs>
    <radialGradient id="celebrationGradient" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.2" />
      <stop offset="50%" style="stop-color:#FF6B6B;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#4CAF50;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#celebrationGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#FFD700" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#FFD700" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#FFD700" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#FFD700" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    毕业典礼
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#F5A623">
    从"知识搬运工"到"战法工程师"的蜕变之旅
  </text>
  
  <!-- 毕业帽装饰 -->
  <g transform="translate(960, 350)">
    <!-- 帽子主体 -->
    <ellipse cx="0" cy="0" rx="80" ry="20" fill="#000000" />
    <rect x="-60" y="-30" width="120" height="30" fill="#000000" rx="15" />
    
    <!-- 帽顶 -->
    <rect x="-100" y="-35" width="200" height="8" fill="#000000" rx="4" />
    
    <!-- 流苏 -->
    <line x1="80" y1="-30" x2="100" y2="-10" stroke="#FFD700" stroke-width="4" />
    <circle cx="100" cy="-10" r="6" fill="#FFD700" />
    
    <!-- 装饰带 -->
    <rect x="-60" y="-25" width="120" height="8" fill="#005A9E" />
  </g>
  
  <!-- 成就徽章 -->
  <g transform="translate(400, 500)">
    <circle cx="0" cy="0" r="60" fill="#FFD700" />
    <circle cx="0" cy="0" r="45" fill="#F5A623" />
    <polygon points="0,-25 15,0 0,25 -15,0" fill="#FFFFFF" />
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#F5A623">
      战法工程师
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      认证徽章
    </text>
  </g>
  
  <g transform="translate(1520, 500)">
    <circle cx="0" cy="0" r="60" fill="#4CAF50" />
    <circle cx="0" cy="0" r="45" fill="#2E7D32" />
    <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="#FFFFFF" stroke-width="4" />
    <path d="M -8 0 L 0 8 L 15 -7" stroke="#FFFFFF" stroke-width="4" fill="none" />
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18" font-weight="bold" fill="#4CAF50">
      实战认证
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      合格证书
    </text>
  </g>
  
  <!-- 核心成就 -->
  <rect x="600" y="450" width="720" height="300" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="500" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    你们已经掌握：
  </text>
  
  <g transform="translate(650, 540)">
    <circle cx="0" cy="0" r="8" fill="#4CAF50" />
    <text x="20" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      精准寻源技术 - 发现真正的"母版"
    </text>
  </g>
  
  <g transform="translate(650, 580)">
    <circle cx="0" cy="0" r="8" fill="#4CAF50" />
    <text x="20" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      深度萃取技术 - 将经验转化为工具
    </text>
  </g>
  
  <g transform="translate(650, 620)">
    <circle cx="0" cy="0" r="8" fill="#4CAF50" />
    <text x="20" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      工具再造技术 - 设计"傻瓜式"清单
    </text>
  </g>
  
  <g transform="translate(650, 660)">
    <circle cx="0" cy="0" r="8" fill="#4CAF50" />
    <text x="20" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      战法植入技术 - 三步法培养能力
    </text>
  </g>
  
  <!-- 庆祝彩带 -->
  <g transform="translate(200, 200)" opacity="0.7">
    <path d="M 0 0 Q 50 20 100 0 Q 150 -20 200 0" stroke="#FF6B6B" stroke-width="8" fill="none" />
    <path d="M 0 30 Q 50 50 100 30 Q 150 10 200 30" stroke="#4CAF50" stroke-width="8" fill="none" />
    <path d="M 0 60 Q 50 80 100 60 Q 150 40 200 60" stroke="#FFD700" stroke-width="8" fill="none" />
  </g>
  
  <g transform="translate(1520, 200)" opacity="0.7">
    <path d="M 0 0 Q -50 20 -100 0 Q -150 -20 -200 0" stroke="#FF6B6B" stroke-width="8" fill="none" />
    <path d="M 0 30 Q -50 50 -100 30 Q -150 10 -200 30" stroke="#4CAF50" stroke-width="8" fill="none" />
    <path d="M 0 60 Q -50 80 -100 60 Q -150 40 -200 60" stroke="#FFD700" stroke-width="8" fill="none" />
  </g>
  
  <!-- 底部祝贺 -->
  <rect x="200" y="800" width="1520" height="150" fill="#FFD700" opacity="0.1" rx="20" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    恭喜各位！
  </text>
  <text x="960" y="910" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    你们已经从"知识搬运工"蜕变为真正的"战法工程师"！
  </text>
  
  <!-- 装饰烟花 -->
  <g transform="translate(300, 300)" opacity="0.6">
    <circle cx="0" cy="0" r="3" fill="#FFD700" />
    <line x1="0" y1="0" x2="0" y2="-20" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="14" y2="-14" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="20" y2="0" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="14" y2="14" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="0" y2="20" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="-14" y2="14" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="-20" y2="0" stroke="#FFD700" stroke-width="2" />
    <line x1="0" y1="0" x2="-14" y2="-14" stroke="#FFD700" stroke-width="2" />
  </g>
  
  <g transform="translate(1620, 350)" opacity="0.6">
    <circle cx="0" cy="0" r="3" fill="#FF6B6B" />
    <line x1="0" y1="0" x2="0" y2="-15" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="11" y2="-11" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="15" y2="0" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="11" y2="11" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="0" y2="15" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="-11" y2="11" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="-15" y2="0" stroke="#FF6B6B" stroke-width="2" />
    <line x1="0" y1="0" x2="-11" y2="-11" stroke="#FF6B6B" stroke-width="2" />
  </g>
</svg>
