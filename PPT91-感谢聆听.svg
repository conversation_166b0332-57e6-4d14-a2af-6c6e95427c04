<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 优雅背景 -->
  <defs>
    <radialGradient id="elegantGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#F0F8FF;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#E6F3FF;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#CCE7FF;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#elegantGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#005A9E" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#005A9E" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#005A9E" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#005A9E" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#005A9E">
    感谢聆听
  </text>
  
  <!-- 英文标题 -->
  <text x="960" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#F5A623">
    Thank You for Your Attention
  </text>
  
  <!-- 讲师信息 -->
  <rect x="400" y="450" width="1120" height="300" fill="#FFFFFF" opacity="0.9" stroke="#005A9E" stroke-width="3" rx="20" />
  
  <!-- 讲师头像装饰 -->
  <g transform="translate(500, 550)">
    <circle cx="0" cy="0" r="60" fill="#005A9E" />
    <circle cx="0" cy="-15" r="20" fill="#F5DEB3" />
    <rect x="-25" y="10" width="50" height="50" fill="#005A9E" />
    <rect x="-20" y="15" width="40" height="12" fill="#FFFFFF" />
  </g>
  
  <!-- 讲师详细信息 -->
  <g transform="translate(600, 500)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#005A9E">
      主讲讲师：[讲师姓名]
    </text>
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      战法工程师认证导师
    </text>
    <text x="0" y="70" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      组织能力建设专家
    </text>
    <text x="0" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
      企业培训体系设计师
    </text>
  </g>
  
  <!-- 联系方式 -->
  <g transform="translate(600, 620)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#F5A623">
      联系方式：
    </text>
    <text x="0" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      📧 邮箱：<EMAIL>
    </text>
    <text x="0" y="65" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      📱 微信：XXXXXXXX
    </text>
    <text x="0" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" fill="#333333">
      🌐 官网：www.example.com
    </text>
  </g>
  
  <!-- 公司LOGO区域 -->
  <g transform="translate(1200, 580)">
    <rect x="0" y="0" width="200" height="100" fill="#005A9E" opacity="0.1" rx="15" />
    <text x="100" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" font-weight="bold" fill="#005A9E">
      公司LOGO
    </text>
    <text x="100" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#666666">
      Company Logo
    </text>
  </g>
  
  <!-- 底部感谢语 -->
  <rect x="200" y="800" width="1520" height="150" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    感谢各位的参与和支持！
  </text>
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    期待与您在战法工程师的道路上再次相遇！
  </text>
  
  <!-- 装饰花朵 -->
  <g transform="translate(300, 400)" opacity="0.6">
    <!-- 花瓣 -->
    <ellipse cx="0" cy="-15" rx="8" ry="15" fill="#FFB6C1" transform="rotate(0)" />
    <ellipse cx="0" cy="-15" rx="8" ry="15" fill="#FFB6C1" transform="rotate(72)" />
    <ellipse cx="0" cy="-15" rx="8" ry="15" fill="#FFB6C1" transform="rotate(144)" />
    <ellipse cx="0" cy="-15" rx="8" ry="15" fill="#FFB6C1" transform="rotate(216)" />
    <ellipse cx="0" cy="-15" rx="8" ry="15" fill="#FFB6C1" transform="rotate(288)" />
    <!-- 花心 -->
    <circle cx="0" cy="0" r="5" fill="#FFD700" />
  </g>
  
  <g transform="translate(1620, 450)" opacity="0.6">
    <!-- 花瓣 -->
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(0)" />
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(60)" />
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(120)" />
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(180)" />
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(240)" />
    <ellipse cx="0" cy="-12" rx="6" ry="12" fill="#DDA0DD" transform="rotate(300)" />
    <!-- 花心 -->
    <circle cx="0" cy="0" r="4" fill="#F5A623" />
  </g>
  
  <!-- 装饰蝴蝶 -->
  <g transform="translate(200, 300)" opacity="0.5">
    <!-- 左翅膀 -->
    <ellipse cx="-8" cy="-5" rx="12" ry="8" fill="#87CEEB" />
    <ellipse cx="-8" cy="5" rx="10" ry="6" fill="#87CEEB" />
    <!-- 右翅膀 -->
    <ellipse cx="8" cy="-5" rx="12" ry="8" fill="#87CEEB" />
    <ellipse cx="8" cy="5" rx="10" ry="6" fill="#87CEEB" />
    <!-- 身体 -->
    <ellipse cx="0" cy="0" rx="2" ry="15" fill="#8B4513" />
    <!-- 触角 -->
    <line x1="0" y1="-15" x2="-3" y2="-20" stroke="#8B4513" stroke-width="1" />
    <line x1="0" y1="-15" x2="3" y2="-20" stroke="#8B4513" stroke-width="1" />
    <circle cx="-3" cy="-20" r="1" fill="#8B4513" />
    <circle cx="3" cy="-20" r="1" fill="#8B4513" />
  </g>
  
  <!-- 装饰叶子 -->
  <g transform="translate(1700, 350)" opacity="0.5">
    <path d="M 0 0 Q -15 -20 -5 -40 Q 5 -45 15 -35 Q 20 -15 5 5 Q -5 10 0 0" fill="#90EE90" />
    <line x1="0" y1="0" x2="0" y2="-20" stroke="#228B22" stroke-width="1" />
  </g>
  
  <g transform="translate(220, 500)" opacity="0.5">
    <path d="M 0 0 Q -12 -15 -3 -30 Q 3 -35 12 -28 Q 15 -12 3 3 Q -3 8 0 0" fill="#98FB98" />
    <line x1="0" y1="0" x2="0" y2="-15" stroke="#228B22" stroke-width="1" />
  </g>
  
  <!-- 装饰边框线 -->
  <line x1="200" y1="200" x2="1720" y2="200" stroke="#005A9E" stroke-width="4" />
  <line x1="300" y1="220" x2="1620" y2="220" stroke="#F5A623" stroke-width="2" />
  
  <!-- 装饰圆点 -->
  <circle cx="250" cy="200" r="8" fill="#005A9E" />
  <circle cx="1670" cy="200" r="8" fill="#005A9E" />
  <circle cx="350" cy="220" r="4" fill="#F5A623" />
  <circle cx="1570" cy="220" r="4" fill="#F5A623" />
  
  <!-- 装饰心形 -->
  <g transform="translate(960, 150)" opacity="0.4">
    <path d="M 0 15 C 0 10, 8 0, 15 0 C 22 0, 30 10, 30 15 C 30 25, 15 40, 0 55 C -15 40, -30 25, -30 15 C -30 10, -22 0, -15 0 C -8 0, 0 10, 0 15 Z" fill="#FF69B4" />
  </g>
</svg>
