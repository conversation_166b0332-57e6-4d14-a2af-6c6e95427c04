<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 卡通化背景 -->
  <defs>
    <radialGradient id="playfulGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#FFE4B5;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#F0F8FF;stop-opacity:0.2" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#playfulGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    体验式游戏：教会机器人折纸飞机
  </text>
  
  <!-- 角色分配区域 -->
  <rect x="200" y="200" width="1520" height="200" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    角色分配:
  </text>
  
  <!-- 专家角色 -->
  <rect x="300" y="280" width="500" height="100" fill="#FFFFFF" opacity="0.9" rx="15" />
  <circle cx="400" cy="330" r="30" fill="#F5A623" opacity="0.7" />
  <text x="400" y="340" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
    专家
  </text>
  <text x="500" y="340" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    1位"专家": 擅长折纸飞机
  </text>
  
  <!-- 机器人角色 -->
  <rect x="1120" y="280" width="500" height="100" fill="#FFFFFF" opacity="0.9" rx="15" />
  <rect x="1220" y="300" width="60" height="60" fill="#005A9E" opacity="0.7" rx="10" />
  <circle cx="1240" cy="320" r="8" fill="#FFFFFF" />
  <circle cx="1260" cy="320" r="8" fill="#FFFFFF" />
  <rect x="1235" y="340" width="30" height="10" fill="#FFFFFF" rx="5" />
  <text x="1320" y="340" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    1位"机器人": 对此一无所知
  </text>
  <text x="1320" y="370" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#F5A623">
    (需佩戴眼罩)
  </text>
  
  <!-- 任务区域 -->
  <rect x="200" y="450" width="1520" height="150" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="500" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    任务:
  </text>
  <text x="960" y="550" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#333333">
    "专家"通过纯语言指令，在2分钟内，教会"机器人"折出一个纸飞机。
  </text>
  
  <!-- 规则区域 -->
  <rect x="200" y="650" width="1520" height="200" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="700" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#F5A623">
    规则:
  </text>
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" fill="#333333">
    "机器人"只能100%执行指令，不能提问和猜测。
  </text>
  
  <!-- 纸飞机装饰 -->
  <polygon points="1500,500 1580,520 1500,540 1520,520" fill="#F5A623" opacity="0.6" />
  <line x1="1520" y1="520" x2="1540" y2="510" stroke="#005A9E" stroke-width="2" />
  <line x1="1520" y1="520" x2="1540" y2="530" stroke="#005A9E" stroke-width="2" />
</svg>
