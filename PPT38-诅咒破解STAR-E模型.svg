<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    诅咒的破解之法：STAR-E模型
  </text>
  
  <!-- 大脑被钥匙打开的图标 -->
  <g transform="translate(400, 300)">
    <!-- 大脑 -->
    <ellipse cx="0" cy="0" rx="80" ry="60" fill="#F5A623" opacity="0.3" />
    <path d="M -60 -30 Q 0 -60 60 -30 Q 80 0 60 30 Q 0 60 -60 30 Q -80 0 -60 -30" fill="#F5A623" opacity="0.6" />
    
    <!-- 钥匙 -->
    <rect x="60" y="-10" width="80" height="20" fill="#005A9E" rx="10" />
    <circle cx="150" cy="0" r="25" fill="#005A9E" />
    <circle cx="150" cy="0" r="15" fill="#FFFFFF" />
    <rect x="140" y="-5" width="20" height="3" fill="#005A9E" />
    <rect x="140" y="2" width="15" height="3" fill="#005A9E" />
    <rect x="140" y="7" width="10" height="3" fill="#005A9E" />
  </g>
  
  <!-- 核心理念 -->
  <rect x="700" y="280" width="1000" height="200" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1200" y="340" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" fill="#005A9E">
    我们需要的不是让专家"教"
  </text>
  <text x="1200" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" fill="#005A9E">
    而是帮助专家"重返现场"
  </text>
  
  <!-- STAR-E logo动画效果 -->
  <g transform="translate(960, 600)">
    <circle cx="0" cy="0" r="150" fill="#F5A623" opacity="0.1" />
    
    <!-- S -->
    <rect x="-120" y="-20" width="40" height="40" fill="#005A9E" rx="5" />
    <text x="-100" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">S</text>
    
    <!-- T -->
    <rect x="-60" y="-20" width="40" height="40" fill="#F5A623" rx="5" />
    <text x="-40" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">T</text>
    
    <!-- A -->
    <rect x="0" y="-20" width="40" height="40" fill="#005A9E" rx="5" />
    <text x="20" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">A</text>
    
    <!-- R -->
    <rect x="60" y="-20" width="40" height="40" fill="#F5A623" rx="5" />
    <text x="80" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">R</text>
    
    <!-- E -->
    <rect x="120" y="-20" width="40" height="40" fill="#005A9E" rx="5" />
    <text x="140" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">E</text>
    
    <!-- 连接线 -->
    <line x1="-80" y1="0" x2="-60" y2="0" stroke="#333333" stroke-width="3" />
    <line x1="-20" y1="0" x2="0" y2="0" stroke="#333333" stroke-width="3" />
    <line x1="40" y1="0" x2="60" y2="0" stroke="#333333" stroke-width="3" />
    <line x1="100" y1="0" x2="120" y2="0" stroke="#333333" stroke-width="3" />
  </g>
  
  <!-- 底部说明 -->
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    这把手术刀，就是STAR-E模型。
  </text>
</svg>
