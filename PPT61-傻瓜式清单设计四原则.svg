<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    核心工艺："傻瓜式"清单设计四原则
  </text>
  
  <!-- 四个原则卡片 -->
  <!-- 原则一 -->
  <rect x="200" y="250" width="320" height="400" fill="#FFFFFF" stroke="#005A9E" stroke-width="4" rx="20" />
  <rect x="200" y="250" width="320" height="80" fill="#005A9E" opacity="0.1" rx="20" />
  
  <!-- 行动图标 -->
  <g transform="translate(360, 300)">
    <circle cx="0" cy="0" r="25" fill="#F5A623" />
    <path d="M -10 -5 L 0 5 L 10 -5" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-linecap="round" />
    <circle cx="0" cy="15" r="3" fill="#FFFFFF" />
  </g>
  
  <text x="360" y="370" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#005A9E">
    原则一
  </text>
  <text x="360" y="420" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
    指令以"动词"开头
  </text>
  
  <!-- 原则二 -->
  <rect x="560" y="250" width="320" height="400" fill="#FFFFFF" stroke="#F5A623" stroke-width="4" rx="20" />
  <rect x="560" y="250" width="320" height="80" fill="#F5A623" opacity="0.1" rx="20" />
  
  <!-- 眼睛图标 -->
  <g transform="translate(720, 300)">
    <ellipse cx="0" cy="0" rx="25" ry="15" fill="#005A9E" />
    <circle cx="0" cy="0" r="8" fill="#FFFFFF" />
    <circle cx="0" cy="0" r="4" fill="#005A9E" />
  </g>
  
  <text x="720" y="370" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
    原则二
  </text>
  <text x="720" y="420" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
    信息"可视化"呈现
  </text>
  
  <!-- 原则三 -->
  <rect x="920" y="250" width="320" height="400" fill="#FFFFFF" stroke="#005A9E" stroke-width="4" rx="20" />
  <rect x="920" y="250" width="320" height="80" fill="#005A9E" opacity="0.1" rx="20" />
  
  <!-- 数字7图标 -->
  <g transform="translate(1080, 300)">
    <circle cx="0" cy="0" r="25" fill="#F5A623" />
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">
      7
    </text>
  </g>
  
  <text x="1080" y="370" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#005A9E">
    原则三
  </text>
  <text x="1080" y="420" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
    遵守"7±2"原则
  </text>
  
  <!-- 原则四 -->
  <rect x="1280" y="250" width="320" height="400" fill="#FFFFFF" stroke="#F5A623" stroke-width="4" rx="20" />
  <rect x="1280" y="250" width="320" height="80" fill="#F5A623" opacity="0.1" rx="20" />
  
  <!-- 复选框图标 -->
  <g transform="translate(1440, 300)">
    <rect x="-20" y="-20" width="40" height="40" fill="none" stroke="#005A9E" stroke-width="4" rx="5" />
    <path d="M -8 0 L -2 6 L 12 -8" stroke="#F5A623" stroke-width="4" fill="none" stroke-linecap="round" />
  </g>
  
  <text x="1440" y="370" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" font-weight="bold" fill="#F5A623">
    原则四
  </text>
  <text x="1440" y="420" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
    提供"可交互"设计
  </text>
</svg>
