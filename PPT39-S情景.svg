<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64" font-weight="bold" fill="#005A9E">
    (S) Situation 情景
  </text>
  
  <!-- 核心目标 -->
  <rect x="200" y="200" width="1520" height="120" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#005A9E">
    核心目标：绘制一张高清的"作战地图"
  </text>
  
  <!-- 导演勘景图标 -->
  <g transform="translate(300, 400)">
    <!-- 摄像机 -->
    <rect x="0" y="20" width="80" height="50" fill="#005A9E" rx="10" />
    <circle cx="90" cy="45" r="25" fill="#F5A623" />
    <circle cx="90" cy="45" r="15" fill="#333333" />
    <rect x="115" y="35" width="30" height="20" fill="#005A9E" />
    
    <!-- 导演 -->
    <circle cx="40" cy="0" r="15" fill="#F5A623" opacity="0.7" />
    <rect x="30" y="15" width="20" height="30" fill="#005A9E" opacity="0.7" />
    <line x1="20" y1="25" x2="10" y2="35" stroke="#005A9E" stroke-width="3" />
    <line x1="50" y1="25" x2="60" y2="35" stroke="#005A9E" stroke-width="3" />
  </g>
  
  <!-- 关键提问范例 -->
  <rect x="600" y="350" width="1100" height="400" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1150" y="410" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    关键提问范例：
  </text>
  
  <rect x="650" y="450" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="490" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "带我回到那个时刻，当时的时间、地点、
  </text>
  <text x="1150" y="530" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    人物、氛围是怎样的？"
  </text>
  
  <rect x="650" y="590" width="1000" height="120" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="1150" y="630" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    "在你开口前，你观察到的、最重要的
  </text>
  <text x="1150" y="670" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    一个细节是什么？"
  </text>
  
  <!-- 地图装饰 -->
  <rect x="200" y="800" width="300" height="200" fill="#F5DEB3" opacity="0.6" rx="10" />
  <path d="M 230 830 Q 280 820 330 840 Q 370 860 420 880 Q 450 900 470 920" stroke="#005A9E" stroke-width="4" fill="none" />
  <circle cx="470" cy="920" r="8" fill="#F5A623" />
  <circle cx="330" cy="840" r="5" fill="#005A9E" />
  <circle cx="420" cy="880" r="6" fill="#F5A623" />
</svg>
