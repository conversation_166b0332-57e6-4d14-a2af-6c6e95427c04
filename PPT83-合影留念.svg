<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 温馨背景 -->
  <defs>
    <radialGradient id="warmGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#FFF8DC;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#F0E68C;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#DDD;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#warmGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    合影留念
  </text>
  
  <!-- 相机装饰 -->
  <g transform="translate(960, 250)">
    <!-- 相机主体 -->
    <rect x="-60" y="-30" width="120" height="60" fill="#333333" rx="10" />
    <rect x="-50" y="-20" width="100" height="40" fill="#666666" rx="5" />
    
    <!-- 镜头 -->
    <circle cx="0" cy="0" r="25" fill="#000000" />
    <circle cx="0" cy="0" r="18" fill="#4169E1" opacity="0.7" />
    <circle cx="0" cy="0" r="10" fill="#87CEEB" opacity="0.5" />
    
    <!-- 闪光灯 -->
    <rect x="-40" y="-25" width="15" height="8" fill="#FFFFFF" rx="2" />
    
    <!-- 快门按钮 -->
    <circle cx="30" cy="-20" r="5" fill="#FF6B6B" />
    
    <!-- 取景器 -->
    <rect x="15" y="-35" width="20" height="8" fill="#333333" rx="2" />
  </g>
  
  <!-- 模拟合影场景 -->
  <rect x="300" y="350" width="1320" height="500" fill="#E6F3FF" opacity="0.3" rx="20" />
  
  <!-- 后排人员 -->
  <g transform="translate(400, 450)">
    <!-- 讲师 -->
    <circle cx="0" cy="0" r="35" fill="#005A9E" />
    <rect x="-25" y="35" width="50" height="60" fill="#005A9E" />
    <line x1="-35" y1="50" x2="-55" y2="60" stroke="#005A9E" stroke-width="5" />
    <line x1="35" y1="50" x2="55" y2="60" stroke="#005A9E" stroke-width="5" />
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16" fill="#005A9E">
      讲师
    </text>
  </g>
  
  <!-- 学员们 -->
  <g transform="translate(600, 450)">
    <circle cx="0" cy="0" r="30" fill="#4CAF50" />
    <rect x="-20" y="30" width="40" height="50" fill="#4CAF50" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#4CAF50" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#4CAF50" stroke-width="4" />
  </g>
  
  <g transform="translate(750, 450)">
    <circle cx="0" cy="0" r="30" fill="#F5A623" />
    <rect x="-20" y="30" width="40" height="50" fill="#F5A623" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#F5A623" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#F5A623" stroke-width="4" />
  </g>
  
  <g transform="translate(900, 450)">
    <circle cx="0" cy="0" r="30" fill="#9C27B0" />
    <rect x="-20" y="30" width="40" height="50" fill="#9C27B0" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#9C27B0" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#9C27B0" stroke-width="4" />
  </g>
  
  <g transform="translate(1050, 450)">
    <circle cx="0" cy="0" r="30" fill="#FF6B6B" />
    <rect x="-20" y="30" width="40" height="50" fill="#FF6B6B" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#FF6B6B" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#FF6B6B" stroke-width="4" />
  </g>
  
  <g transform="translate(1200, 450)">
    <circle cx="0" cy="0" r="30" fill="#00BCD4" />
    <rect x="-20" y="30" width="40" height="50" fill="#00BCD4" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#00BCD4" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#00BCD4" stroke-width="4" />
  </g>
  
  <g transform="translate(1350, 450)">
    <circle cx="0" cy="0" r="30" fill="#795548" />
    <rect x="-20" y="30" width="40" height="50" fill="#795548" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#795548" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#795548" stroke-width="4" />
  </g>
  
  <g transform="translate(1500, 450)">
    <circle cx="0" cy="0" r="30" fill="#607D8B" />
    <rect x="-20" y="30" width="40" height="50" fill="#607D8B" />
    <line x1="-30" y1="45" x2="-45" y2="55" stroke="#607D8B" stroke-width="4" />
    <line x1="30" y1="45" x2="45" y2="55" stroke="#607D8B" stroke-width="4" />
  </g>
  
  <!-- 前排学员 -->
  <g transform="translate(500, 600)">
    <circle cx="0" cy="0" r="28" fill="#E91E63" />
    <rect x="-18" y="28" width="36" height="45" fill="#E91E63" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#E91E63" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#E91E63" stroke-width="4" />
  </g>
  
  <g transform="translate(650, 600)">
    <circle cx="0" cy="0" r="28" fill="#3F51B5" />
    <rect x="-18" y="28" width="36" height="45" fill="#3F51B5" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#3F51B5" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#3F51B5" stroke-width="4" />
  </g>
  
  <g transform="translate(800, 600)">
    <circle cx="0" cy="0" r="28" fill="#FF9800" />
    <rect x="-18" y="28" width="36" height="45" fill="#FF9800" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#FF9800" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#FF9800" stroke-width="4" />
  </g>
  
  <g transform="translate(950, 600)">
    <circle cx="0" cy="0" r="28" fill="#8BC34A" />
    <rect x="-18" y="28" width="36" height="45" fill="#8BC34A" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#8BC34A" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#8BC34A" stroke-width="4" />
  </g>
  
  <g transform="translate(1100, 600)">
    <circle cx="0" cy="0" r="28" fill="#FF5722" />
    <rect x="-18" y="28" width="36" height="45" fill="#FF5722" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#FF5722" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#FF5722" stroke-width="4" />
  </g>
  
  <g transform="translate(1250, 600)">
    <circle cx="0" cy="0" r="28" fill="#009688" />
    <rect x="-18" y="28" width="36" height="45" fill="#009688" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#009688" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#009688" stroke-width="4" />
  </g>
  
  <g transform="translate(1400, 600)">
    <circle cx="0" cy="0" r="28" fill="#673AB7" />
    <rect x="-18" y="28" width="36" height="45" fill="#673AB7" />
    <line x1="-28" y1="40" x2="-40" y2="50" stroke="#673AB7" stroke-width="4" />
    <line x1="28" y1="40" x2="40" y2="50" stroke="#673AB7" stroke-width="4" />
  </g>
  
  <!-- 底部纪念文字 -->
  <rect x="200" y="900" width="1520" height="100" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="940" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#005A9E">
    《战法工程师》第一期 · 2024年毕业合影
  </text>
  <text x="960" y="975" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24" fill="#333333">
    从此刻起，你们将在各自的战场上发光发热！
  </text>
  
  <!-- 装饰相框 -->
  <rect x="280" y="330" width="1360" height="540" fill="none" stroke="#8B4513" stroke-width="12" rx="15" />
  <rect x="290" y="340" width="1340" height="520" fill="none" stroke="#D2691E" stroke-width="6" rx="10" />
  
  <!-- 装饰心形 -->
  <g transform="translate(200, 400)" opacity="0.6">
    <path d="M 0 10 C 0 5, 5 0, 10 0 C 15 0, 20 5, 20 10 C 20 15, 10 25, 0 35 C -10 25, -20 15, -20 10 C -20 5, -15 0, -10 0 C -5 0, 0 5, 0 10 Z" fill="#FF69B4" />
  </g>
  
  <g transform="translate(1720, 600)" opacity="0.6">
    <path d="M 0 8 C 0 4, 4 0, 8 0 C 12 0, 16 4, 16 8 C 16 12, 8 20, 0 28 C -8 20, -16 12, -16 8 C -16 4, -12 0, -8 0 C -4 0, 0 4, 0 8 Z" fill="#FF69B4" />
  </g>
</svg>
