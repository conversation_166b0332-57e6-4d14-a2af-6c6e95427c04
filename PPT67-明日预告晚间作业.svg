<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    明日预告 & 晚间作业
  </text>
  
  <!-- 明日预告区域 -->
  <rect x="200" y="200" width="1520" height="350" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="960" y="260" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    明日预告：
  </text>
  
  <text x="960" y="320" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48" font-weight="bold" fill="#F5A623">
    Day 3: 赋能植入与行动启航
  </text>
  
  <!-- 士兵分发武器图标 -->
  <g transform="translate(400, 400)">
    <!-- 教练 -->
    <circle cx="0" cy="0" r="25" fill="#005A9E" />
    <rect x="-15" y="25" width="30" height="40" fill="#005A9E" />
    <line x1="-25" y1="35" x2="-35" y2="45" stroke="#005A9E" stroke-width="4" />
    <line x1="25" y1="35" x2="35" y2="45" stroke="#005A9E" stroke-width="4" />
    
    <!-- 武器 -->
    <rect x="50" y="30" width="40" height="20" fill="#F5A623" rx="5" />
    <text x="70" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#FFFFFF">
      工具
    </text>
    
    <!-- 箭头 -->
    <path d="M 35 40 L 45 40" stroke="#F5A623" stroke-width="3" marker-end="url(#arrowhead)" />
    
    <!-- 士兵 -->
    <circle cx="120" cy="0" r="20" fill="#4CAF50" />
    <rect x="-108" y="20" width="25" height="35" fill="#4CAF50" />
    <line x1="95" y1="30" x2="85" y2="40" stroke="#4CAF50" stroke-width="3" />
    <line x1="145" y1="30" x2="155" y2="40" stroke="#4CAF50" stroke-width="3" />
  </g>
  
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    我们将学习如何将锻造好的"武器"，
  </text>
  <text x="960" y="490" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    真正地"植入"到团队的"肌肉记忆"中。
  </text>
  
  <!-- 晚间思考题区域 -->
  <rect x="200" y="600" width="1520" height="300" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="660" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#F5A623">
    晚间思考题：
  </text>
  
  <rect x="250" y="700" width="1420" height="180" fill="#FFFFFF" opacity="0.9" rx="15" />
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    看着你设计的这份清单，如果让你去教会一个新人使用它，
  </text>
  <text x="960" y="790" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    你认为，他最容易在哪个环节犯错？
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28" fill="#333333">
    或者，哪个环节是他理解起来最困难的？
  </text>
  
  <!-- 思考装饰 -->
  <g transform="translate(1500, 750)">
    <circle cx="0" cy="0" r="40" fill="#F5A623" opacity="0.2" />
    <circle cx="0" cy="0" r="25" fill="#F5A623" opacity="0.4" />
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#F5A623">
      ?
    </text>
  </g>
  
  <!-- 装饰思考泡泡 -->
  <g transform="translate(300, 750)" opacity="0.6">
    <circle cx="0" cy="0" r="15" fill="#005A9E" opacity="0.3" />
    <circle cx="20" cy="-10" r="10" fill="#005A9E" opacity="0.4" />
    <circle cx="35" cy="-20" r="6" fill="#005A9E" opacity="0.5" />
  </g>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
</svg>
