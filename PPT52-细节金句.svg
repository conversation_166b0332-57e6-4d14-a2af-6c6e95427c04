<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 背景装饰 -->
  <defs>
    <radialGradient id="detailGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#F5A623;stop-opacity:0.05" />
      <stop offset="100%" style="stop-color:#005A9E;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#detailGradient)" />
  
  <!-- 放大镜装饰 -->
  <g transform="translate(300, 300)">
    <circle cx="0" cy="0" r="60" fill="none" stroke="#F5A623" stroke-width="8" />
    <circle cx="0" cy="0" r="40" fill="none" stroke="#F5A623" stroke-width="4" opacity="0.6" />
    <line x1="42" y1="42" x2="80" y2="80" stroke="#F5A623" stroke-width="8" />
    <circle cx="0" cy="0" r="20" fill="#F5A623" opacity="0.2" />
  </g>
  
  <g transform="translate(1620, 780)">
    <circle cx="0" cy="0" r="50" fill="none" stroke="#005A9E" stroke-width="6" />
    <circle cx="0" cy="0" r="30" fill="none" stroke="#005A9E" stroke-width="3" opacity="0.6" />
    <line x1="35" y1="35" x2="65" y2="65" stroke="#005A9E" stroke-width="6" />
    <circle cx="0" cy="0" r="15" fill="#005A9E" opacity="0.2" />
  </g>
  
  <!-- 主要金句 -->
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#005A9E">
    放弃归纳，只要细节。
  </text>
  
  <text x="960" y="580" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="80" font-weight="bold" fill="#F5A623">
    细节，细节，还是细节！
  </text>
  
  <!-- 装饰线条 -->
  <line x1="200" y1="700" x2="1720" y2="700" stroke="#F5A623" stroke-width="8" />
  <line x1="300" y1="720" x2="1620" y2="720" stroke="#005A9E" stroke-width="6" />
  
  <!-- 细节强调装饰 -->
  <circle cx="200" cy="450" r="12" fill="#F5A623" />
  <circle cx="220" cy="470" r="8" fill="#F5A623" opacity="0.7" />
  <circle cx="240" cy="490" r="6" fill="#F5A623" opacity="0.5" />
  <circle cx="260" cy="510" r="4" fill="#F5A623" opacity="0.3" />
  
  <circle cx="1720" cy="580" r="12" fill="#005A9E" />
  <circle cx="1700" cy="560" r="8" fill="#005A9E" opacity="0.7" />
  <circle cx="1680" cy="540" r="6" fill="#005A9E" opacity="0.5" />
  <circle cx="1660" cy="520" r="4" fill="#005A9E" opacity="0.3" />
  
  <!-- 底部强调 -->
  <rect x="400" y="800" width="1120" height="100" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    带着满满的"矿石"，下午回来学习如何"锻造"！
  </text>
</svg>
