<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 工作台背景 -->
  <defs>
    <radialGradient id="workshopGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#F5DEB3;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#DEB887;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#D2B48C;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#workshopGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 200 Q 120 120 200 120" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1720 960 Q 1800 960 1800 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 页面标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="56" font-weight="bold" fill="#005A9E">
    工具设计实验室：锻造你的第一份"武器"
  </text>
  
  <!-- 工作台装饰 -->
  <g transform="translate(300, 300)">
    <!-- 工作台面 -->
    <rect x="0" y="50" width="200" height="100" fill="#8B4513" rx="10" />
    <rect x="-20" y="150" width="240" height="30" fill="#654321" rx="5" />
    
    <!-- 设计工具 -->
    <rect x="20" y="20" width="60" height="40" fill="#FFFFFF" stroke="#005A9E" stroke-width="2" rx="5" />
    <text x="50" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12" fill="#005A9E">
      草图
    </text>
    
    <!-- 彩笔 -->
    <rect x="100" y="10" width="8" height="50" fill="#F5A623" rx="2" />
    <rect x="115" y="10" width="8" height="50" fill="#005A9E" rx="2" />
    <rect x="130" y="10" width="8" height="50" fill="#FF6B6B" rx="2" />
    
    <!-- 尺子 -->
    <rect x="150" y="30" width="40" height="8" fill="#C0C0C0" rx="2" />
    <line x1="155" y1="30" x2="155" y2="38" stroke="#333333" stroke-width="1" />
    <line x1="165" y1="30" x2="165" y2="38" stroke="#333333" stroke-width="1" />
    <line x1="175" y1="30" x2="175" y2="38" stroke="#333333" stroke-width="1" />
    <line x1="185" y1="30" x2="185" y2="38" stroke="#333333" stroke-width="1" />
  </g>
  
  <!-- 任务区域 -->
  <rect x="600" y="250" width="1100" height="300" fill="#005A9E" opacity="0.1" rx="20" />
  <text x="1150" y="310" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40" font-weight="bold" fill="#005A9E">
    任务：
  </text>
  
  <text x="650" y="370" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 运用"四大设计原则"
  </text>
  
  <text x="650" y="420" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    • 将你的《萃取访谈原始记录稿》，设计成一份
  </text>
  
  <text x="680" y="470" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" font-weight="bold" fill="#F5A623">
    《关键动作检查清单 V1.0》
  </text>
  
  <!-- 要求区域 -->
  <rect x="200" y="600" width="1520" height="200" fill="#F5A623" opacity="0.1" rx="20" />
  <text x="960" y="650" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#F5A623">
    要求：
  </text>
  
  <text x="960" y="700" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32" fill="#333333">
    清晰、可执行，而非大而全
  </text>
  
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36" font-weight="bold" fill="#005A9E">
    时间：60分钟
  </text>
  
  <!-- 装饰齿轮 -->
  <g transform="translate(1500, 400)">
    <circle cx="0" cy="0" r="50" fill="none" stroke="#F5A623" stroke-width="6" opacity="0.4" />
    <circle cx="0" cy="0" r="30" fill="none" stroke="#F5A623" stroke-width="4" opacity="0.6" />
    <circle cx="0" cy="0" r="15" fill="#F5A623" opacity="0.3" />
    <!-- 齿轮齿 -->
    <rect x="-3" y="-58" width="6" height="16" fill="#F5A623" opacity="0.4" />
    <rect x="-3" y="42" width="6" height="16" fill="#F5A623" opacity="0.4" />
    <rect x="42" y="-3" width="16" height="6" fill="#F5A623" opacity="0.4" />
    <rect x="-58" y="-3" width="16" height="6" fill="#F5A623" opacity="0.4" />
  </g>
</svg>
